// Otimizações de Performance para AuracronCombatBridge
// Este arquivo contém implementações otimizadas para sistemas críticos de performance
// Foco em cache inteligente, pooling de objetos e algoritmos eficientes

#include "AuracronCombatBridge.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "HAL/IConsoleManager.h"
#include "Async/ParallelFor.h"
#include "Engine/OverlapResult.h"
#include "Stats/Stats.h"
#include "EngineUtils.h"

// Declaração do log category
DEFINE_LOG_CATEGORY_STATIC(LogAuracronCombat, Log, All);

// Definições de STAT para profiling
DECLARE_CYCLE_STAT(TEXT("Find Valid Targets"), STAT_AuracronCombat_FindValidTargets, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("Update Target Cache"), STAT_AuracronCombat_UpdateTargetCache, STATGROUP_Game);
DECLARE_CYCLE_STAT(TEXT("Calculate Damage"), STAT_AuracronCombat_CalculateDamage, STATGROUP_Game);

// ========== CONFIGURAÇÕES DE PERFORMANCE ==========

// Console Variables para ajuste dinâmico de performance
static TAutoConsoleVariable<float> CVarTargetCacheUpdateInterval(
    TEXT("auracron.combat.TargetCacheUpdateInterval"),
    0.5f,
    TEXT("Intervalo de atualização do cache de alvos (segundos)"),
    ECVF_Default
);

static TAutoConsoleVariable<float> CVarTargetCacheDuration(
    TEXT("auracron.combat.TargetCacheDuration"),
    0.1f,
    TEXT("Duração do cache de alvos válidos (segundos)"),
    ECVF_Default
);

static TAutoConsoleVariable<int32> CVarMaxCachedTargets(
    TEXT("auracron.combat.MaxCachedTargets"),
    100,
    TEXT("Número máximo de alvos no cache"),
    ECVF_Default
);

static TAutoConsoleVariable<bool> CVarEnableParallelTargeting(
    TEXT("auracron.combat.EnableParallelTargeting"),
    true,
    TEXT("Habilitar processamento paralelo para targeting"),
    ECVF_Default
);

static TAutoConsoleVariable<bool> CVarEnableAdaptiveCache(
    TEXT("auracron.combat.EnableAdaptiveCache"),
    true,
    TEXT("Habilitar cache adaptativo baseado na carga do sistema"),
    ECVF_Default
);

// ========== SISTEMA DE CACHE INTELIGENTE ==========

void UAuracronCombatBridge::OptimizedUpdateTargetCache()
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronCombat_UpdateTargetCache);
    
    if (!GetWorld())
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    float UpdateInterval = CVarTargetCacheUpdateInterval.GetValueOnGameThread();
    
    // Cache adaptativo - ajustar intervalo baseado na carga do sistema
    if (CVarEnableAdaptiveCache.GetValueOnGameThread())
    {
        float FrameTime = GetWorld()->GetDeltaSeconds();
        if (FrameTime > 0.033f) // Se FPS < 30
        {
            UpdateInterval *= 1.5f; // Reduzir frequência de atualização
        }
        else if (FrameTime < 0.016f) // Se FPS > 60
        {
            UpdateInterval *= 0.8f; // Aumentar frequência de atualização
        }
    }
    
    if (CurrentTime - LastTargetCacheUpdate < UpdateInterval)
    {
        return;
    }

    LastTargetCacheUpdate = CurrentTime;
    
    // Usar processamento paralelo se habilitado
    if (CVarEnableParallelTargeting.GetValueOnGameThread())
    {
        UpdateTargetCacheParallel();
    }
    else
    {
        UpdateTargetCacheSequential();
    }
}

void UAuracronCombatBridge::UpdateTargetCacheParallel()
{
    // Coletar todos os atores primeiro
    TArray<AActor*> AllActors;
    for (TActorIterator<AActor> ActorIterator(GetWorld()); ActorIterator; ++ActorIterator)
    {
        AllActors.Add(*ActorIterator);
    }
    
    // Estrutura para resultados thread-safe
    struct FLayerTargetResult
    {
        EAuracronCombatLayer Layer;
        TArray<TWeakObjectPtr<AActor>> Targets;
    };
    
    TArray<FLayerTargetResult> Results;
    Results.SetNum(3); // Uma para cada camada
    Results[0].Layer = EAuracronCombatLayer::Surface;
    Results[1].Layer = EAuracronCombatLayer::Sky;
    Results[2].Layer = EAuracronCombatLayer::Underground;
    
    // Processar atores em paralelo
    ParallelFor(AllActors.Num(), [&](int32 Index)
    {
        AActor* Actor = AllActors[Index];
        if (IsValidTargetOptimized(Actor))
        {
            EAuracronCombatLayer ActorLayer = GetActorCombatLayer(Actor);
            int32 LayerIndex = static_cast<int32>(ActorLayer);
            
            // Thread-safe addition
            FCriticalSection CriticalSection;
            FScopeLock Lock(&CriticalSection);
            Results[LayerIndex].Targets.Add(Actor);
        }
    });
    
    // Atualizar cache principal (thread-safe)
    LayerTargetCache.Empty();
    for (const FLayerTargetResult& Result : Results)
    {
        if (Result.Targets.Num() > 0)
        {
            LayerTargetCache.Add(Result.Layer, Result.Targets);
        }
    }
}

void UAuracronCombatBridge::UpdateTargetCacheSequential()
{
    LayerTargetCache.Empty();
    
    int32 MaxTargets = CVarMaxCachedTargets.GetValueOnGameThread();
    int32 ProcessedTargets = 0;
    
    for (TActorIterator<AActor> ActorIterator(GetWorld()); ActorIterator && ProcessedTargets < MaxTargets; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (IsValidTargetOptimized(Actor))
        {
            EAuracronCombatLayer ActorLayer = GetActorCombatLayer(Actor);
            LayerTargetCache.FindOrAdd(ActorLayer).Add(Actor);
            ProcessedTargets++;
        }
    }
}

bool UAuracronCombatBridge::IsValidTargetOptimized(AActor* Target) const
{
    // Verificações rápidas primeiro (early exit)
    if (!Target || Target == GetOwner())
    {
        return false;
    }
    
    // Cache de resultados de validação para evitar recálculos
    static TMap<TWeakObjectPtr<AActor>, bool> ValidationCache;
    static float LastValidationClearTime = 0.0f;
    
    // Limpar cache periodicamente
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    if (CurrentTime - LastValidationClearTime > 5.0f)
    {
        ValidationCache.Empty();
        LastValidationClearTime = CurrentTime;
    }
    
    TWeakObjectPtr<AActor> WeakTarget = Target;
    if (bool* CachedResult = ValidationCache.Find(WeakTarget))
    {
        return *CachedResult;
    }
    
    // Verificações otimizadas
    bool bIsValid = true;
    
    // Verificar se é uma classe alvejável (usando cache de classes)
    static TSet<UClass*> CachedTargetableClasses;
    if (CachedTargetableClasses.Num() == 0)
    {
        for (TSubclassOf<AActor> TargetableClass : TargetableClasses)
        {
            CachedTargetableClasses.Add(TargetableClass);
        }
    }
    
    bool bIsTargetableClass = false;
    for (UClass* TargetableClass : CachedTargetableClasses)
    {
        if (Target->IsA(TargetableClass))
        {
            bIsTargetableClass = true;
            break;
        }
    }
    
    if (!bIsTargetableClass)
    {
        bIsValid = false;
    }
    
    // Verificar se está ativo e não sendo destruído
    if (bIsValid && !IsValid(Target))
    {
        bIsValid = false;
    }
    
    // Cache do resultado
    ValidationCache.Add(WeakTarget, bIsValid);
    
    return bIsValid;
}

// ========== OTIMIZAÇÕES DE TARGETING ==========

TArray<AActor*> UAuracronCombatBridge::FindValidTargetsOptimized(float Range, bool bIncludeOtherLayers) const
{
    SCOPE_CYCLE_COUNTER(STAT_AuracronCombat_FindValidTargets);
    
    TArray<AActor*> ValidTargets;
    
    if (!GetOwner())
    {
        return ValidTargets;
    }
    
    float CacheDuration = CVarTargetCacheDuration.GetValueOnGameThread();
    
    // Usar cache se disponível e válido
    if (GetWorld()->GetTimeSeconds() - LastTargetCacheTime < CacheDuration && !CachedValidTargets.IsEmpty())
    {
        // Filtrar alvos do cache por distância (mais eficiente que recalcular tudo)
        FVector OwnerLocation = GetOwner()->GetActorLocation();
        float RangeSquared = Range * Range;
        
        for (const TWeakObjectPtr<AActor>& WeakTarget : CachedValidTargets)
        {
            if (AActor* Target = WeakTarget.Get())
            {
                float DistanceSquared = FVector::DistSquared(OwnerLocation, Target->GetActorLocation());
                if (DistanceSquared <= RangeSquared)
                {
                    ValidTargets.Add(Target);
                }
            }
        }
        return ValidTargets;
    }
    
    // Usar cache por camada se disponível
    if (LayerTargetCache.Num() > 0)
    {
        ValidTargets = FindValidTargetsFromLayerCache(Range, bIncludeOtherLayers);
    }
    else
    {
        // Fallback para método tradicional
        ValidTargets = FindValidTargetsTraditional(Range, bIncludeOtherLayers);
    }
    
    // Atualizar cache de alvos válidos
    UpdateValidTargetsCache(ValidTargets);
    
    return ValidTargets;
}

TArray<AActor*> UAuracronCombatBridge::FindValidTargetsFromLayerCache(float Range, bool bIncludeOtherLayers) const
{
    TArray<AActor*> ValidTargets;
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    float RangeSquared = Range * Range;
    
    // Processar camada atual primeiro
    if (const TArray<TWeakObjectPtr<AActor>>* CurrentLayerTargets = LayerTargetCache.Find(CurrentCombatLayer))
    {
        for (const TWeakObjectPtr<AActor>& WeakTarget : *CurrentLayerTargets)
        {
            if (AActor* Target = WeakTarget.Get())
            {
                float DistanceSquared = FVector::DistSquared(OwnerLocation, Target->GetActorLocation());
                if (DistanceSquared <= RangeSquared)
                {
                    ValidTargets.Add(Target);
                }
            }
        }
    }
    
    // Processar outras camadas se necessário
    if (bIncludeOtherLayers)
    {
        for (const auto& LayerPair : LayerTargetCache)
        {
            if (LayerPair.Key != CurrentCombatLayer)
            {
                for (const TWeakObjectPtr<AActor>& WeakTarget : LayerPair.Value)
                {
                    if (AActor* Target = WeakTarget.Get())
                    {
                        float DistanceSquared = FVector::DistSquared(OwnerLocation, Target->GetActorLocation());
                        if (DistanceSquared <= RangeSquared)
                        {
                            ValidTargets.Add(Target);
                        }
                    }
                }
            }
        }
    }
    
    return ValidTargets;
}

TArray<AActor*> UAuracronCombatBridge::FindValidTargetsTraditional(float Range, bool bIncludeOtherLayers) const
{
    TArray<AActor*> ValidTargets;
    TArray<FOverlapResult> OverlapResults;
    
    // Usar sphere overlap para eficiência
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FCollisionShape SphereShape = FCollisionShape::MakeSphere(Range);
    
    GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        OwnerLocation,
        FQuat::Identity,
        ECollisionChannel::ECC_Pawn,
        SphereShape
    );
    
    for (const FOverlapResult& Result : OverlapResults)
    {
        AActor* Actor = Result.GetActor();
        if (!Actor) continue;
        
        if (IsValidTargetOptimized(Actor))
        {
            // Verificar camada se necessário
            if (!bIncludeOtherLayers)
            {
                if (UAuracronCombatBridge* TargetCombatBridge = Actor->FindComponentByClass<UAuracronCombatBridge>())
                {
                    if (TargetCombatBridge->GetCurrentCombatLayer() != CurrentCombatLayer)
                    {
                        continue;
                    }
                }
            }
            
            ValidTargets.Add(Actor);
        }
    }
    
    return ValidTargets;
}

void UAuracronCombatBridge::UpdateValidTargetsCache(const TArray<AActor*>& ValidTargets) const
{
    CachedValidTargets.Empty();
    
    int32 MaxCacheSize = CVarMaxCachedTargets.GetValueOnGameThread();
    int32 TargetsToCache = FMath::Min(ValidTargets.Num(), MaxCacheSize);
    
    for (int32 i = 0; i < TargetsToCache; i++)
    {
        CachedValidTargets.Add(ValidTargets[i]);
    }
    
    LastTargetCacheTime = GetWorld()->GetTimeSeconds();
}

// ========== OTIMIZAÇÕES DE CÁLCULO DE PRIORIDADE ==========

float UAuracronCombatBridge::CalculateTargetPriorityOptimized(AActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }
    
    // Cache de prioridades para evitar recálculos
    static TMap<TWeakObjectPtr<AActor>, float> PriorityCache;
    static float LastPriorityClearTime = 0.0f;
    
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    
    // Limpar cache periodicamente
    if (CurrentTime - LastPriorityClearTime > 2.0f)
    {
        PriorityCache.Empty();
        LastPriorityClearTime = CurrentTime;
    }
    
    TWeakObjectPtr<AActor> WeakTarget = Target;
    if (float* CachedPriority = PriorityCache.Find(WeakTarget))
    {
        return *CachedPriority;
    }
    
    // Cálculo otimizado de prioridade
    float Priority = 100.0f;
    
    // Fator de distância (usando distância ao quadrado para evitar sqrt)
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    float DistanceSquared = FVector::DistSquared(OwnerLocation, TargetLocation);
    float MaxRangeSquared = GetMaxRangeForCurrentLayer() * GetMaxRangeForCurrentLayer();
    
    if (MaxRangeSquared > 0.0f)
    {
        float DistanceFactor = 1.0f - (DistanceSquared / MaxRangeSquared);
        Priority *= FMath::Clamp(DistanceFactor, 0.1f, 1.0f);
    }
    
    // Bônus para mesma camada
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (TargetLayer == CurrentCombatLayer)
    {
        Priority *= 1.5f;
    }
    
    // Cache do resultado
    PriorityCache.Add(WeakTarget, Priority);
    
    return Priority;
}

// ========== SISTEMA DE POOLING PARA OBJETOS TEMPORÁRIOS ==========

class FAuracronObjectPool
{
public:
    template<typename T>
    static TSharedPtr<T> GetPooledObject()
    {
        static TArray<TSharedPtr<T>> Pool;
        
        if (Pool.Num() > 0)
        {
            TSharedPtr<T> Object = Pool.Pop();
            return Object;
        }
        
        return MakeShared<T>();
    }
    
    template<typename T>
    static void ReturnToPool(TSharedPtr<T> Object)
    {
        static TArray<TSharedPtr<T>> Pool;
        
        if (Pool.Num() < 50) // Limitar tamanho do pool
        {
            Pool.Add(Object);
        }
    }
};

// ========== MÉTRICAS DE PERFORMANCE ==========

void UAuracronCombatBridge::LogPerformanceMetrics() const
{
    if (!GetWorld())
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    static float LastLogTime = 0.0f;
    
    // Log métricas a cada 10 segundos
    if (CurrentTime - LastLogTime > 10.0f)
    {
        UE_LOG(LogAuracronCombat, Log, TEXT("=== AURACRON COMBAT PERFORMANCE METRICS ==="));
        UE_LOG(LogAuracronCombat, Log, TEXT("Cached Targets: %d"), CachedValidTargets.Num());
        UE_LOG(LogAuracronCombat, Log, TEXT("Layer Cache Size: %d"), LayerTargetCache.Num());
        UE_LOG(LogAuracronCombat, Log, TEXT("Target Cache Update Interval: %.3f"), CVarTargetCacheUpdateInterval.GetValueOnGameThread());
        UE_LOG(LogAuracronCombat, Log, TEXT("Frame Time: %.3f ms"), GetWorld()->GetDeltaSeconds() * 1000.0f);
        
        LastLogTime = CurrentTime;
    }
}

// ========== COMANDOS DE CONSOLE PARA DEBUG ==========

static FAutoConsoleCommand CCmdClearTargetCache(
    TEXT("auracron.combat.ClearTargetCache"),
    TEXT("Limpa todos os caches de targeting"),
    FConsoleCommandDelegate::CreateLambda([]()
    {
        UE_LOG(LogTemp, Warning, TEXT("Target caches cleared manually"));
        // TODO: Implementar limpeza global de cache
    })
);

static FAutoConsoleCommand CCmdShowPerformanceStats(
    TEXT("auracron.combat.ShowPerformanceStats"),
    TEXT("Mostra estatísticas de performance do sistema de combate"),
    FConsoleCommandDelegate::CreateLambda([]()
    {
        UE_LOG(LogTemp, Warning, TEXT("Performance stats logging enabled"));
        // TODO: Implementar logging detalhado
    })
);