// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge Implementation

#include "AuracronCombatBridge.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Character.h"
#include "Components/CapsuleComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Engine/Engine.h"
#include "EngineUtils.h"
#include "Engine/OverlapResult.h"
#include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"
#include "AuracronVerticalTransitionsBridge/Public/AuracronVerticalTransitionsBridge.h"
#include "AuracronRealmsBridge/Public/AuracronRealmsBridge.h"
#include "AuracronCharacter.h"

// Logs
DEFINE_LOG_CATEGORY_STATIC(LogAuracronCombat, Log, All);

UAuracronCombatBridge::UAuracronCombatBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização
    SetIsReplicatedByDefault(true);
    
    // Configurar dados padrão das camadas
    SetupDefaultLayerData();
}

void UAuracronCombatBridge::BeginPlay()
{
    Super::BeginPlay();
    
    // Inicializar referências aos outros bridges
    InitializeBridgeReferences();
    
    // Inicializar sistema de tipos de dano
    InitializeDamageTypeModifiers();
    
    // Configurar camada inicial baseada na posição
    if (AActor* Owner = GetOwner())
    {
        FVector Location = Owner->GetActorLocation();
        
        // Lógica simples para determinar camada inicial baseada na altura
        if (Location.Z > 1000.0f)
        {
            SetCombatLayer(EAuracronCombatLayer::Sky);
        }
        else if (Location.Z < -500.0f)
        {
            SetCombatLayer(EAuracronCombatLayer::Underground);
        }
        else
        {
            SetCombatLayer(EAuracronCombatLayer::Surface);
        }
    }
    
    // Inicializar efeitos padrão de camada
    InitializeDefaultLayerEffects();
    
    // Configurar timer para atualização de efeitos
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            LayerEffectsUpdateTimer,
            this,
            &UAuracronCombatBridge::UpdateLayerEffects,
            1.0f, // Atualizar a cada segundo
            true  // Loop
        );
    }
    
    // Inicializar sistema de targeting 3D
    InitializeTargetingSystem();
    
    // Inicializar sistema de status effects
    MaxActiveStatusEffects = 10;
    bAutoApplyVFX = true;
    InitializeStatusEffectTemplates();
    
    // Inicializar configurações de performance
    PerformanceConfig = GetDefault<UAuracronCombatPerformanceConfig>();
    if (PerformanceConfig)
    {
        UE_LOG(LogTemp, Log, TEXT("AuracronCombatBridge: Performance config initialized"));
    }
    
    // Inicializar posição para detecção de transições automáticas
    if (GetOwner())
    {
        PreviousPosition = GetOwner()->GetActorLocation();
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("AuracronCombatBridge initialized for %s"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
}

void UAuracronCombatBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Atualizar cache de alvos se necessário (versão otimizada)
    if (GetWorld()->GetTimeSeconds() - LastTargetCacheTime > TARGET_CACHE_DURATION)
    {
        CachedValidTargets.Empty();
        LastTargetCacheTime = GetWorld()->GetTimeSeconds();
    }
    
    // Atualizar efeitos de camada
    UpdateLayerEffects();
    
    // Verificar transições automáticas de camada
    CheckForAutomaticLayerTransition();
    
    // Atualizar rastreamento de movimento para IA
    UpdateMovementTracking();
    
    // Atualizar sistema de combos especiais
    UpdateSpecialComboSystem(DeltaTime);
    
    // Atualizar sistema de status effects
    UpdateStatusEffects(DeltaTime);
    
    // Atualizar cache de alvos do sistema de targeting 3D (versão otimizada)
    OptimizedUpdateTargetCache();
    
    // Log de métricas de performance (apenas em builds de desenvolvimento)
    #if !UE_BUILD_SHIPPING
    LogPerformanceMetrics();
    #endif
    
    // Atualizar modificadores se em combate
    if (bInCombat)
    {
        UpdateLayerModifiers();
        
        // Atualizar sistema adaptativo
        CheckComboReset();
        UpdateAdaptiveResistances();
    }
}

void UAuracronCombatBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UAuracronCombatBridge, CurrentCombatLayer);
    DOREPLIFETIME(UAuracronCombatBridge, bInCombat);
    DOREPLIFETIME_CONDITION(UAuracronCombatBridge, ActiveLayerEffects, COND_OwnerOnly);
}

// === Combat Layer Management ===

EAuracronCombatLayer UAuracronCombatBridge::GetCurrentCombatLayer() const
{
    return CurrentCombatLayer;
}

void UAuracronCombatBridge::SetCombatLayer(EAuracronCombatLayer NewLayer)
{
    if (CurrentCombatLayer != NewLayer)
    {
        EAuracronCombatLayer OldLayer = CurrentCombatLayer;
        CurrentCombatLayer = NewLayer;
        
        // Atualizar modificadores
        UpdateLayerModifiers();
        
        // Aplicar bônus específicos da nova camada
        ApplyRealmCombatBonuses();
        
        UE_LOG(LogAuracronCombat, Log, TEXT("%s changed combat layer from %d to %d"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"), 
               (int32)OldLayer, (int32)NewLayer);
        
        // Trigger replication
        if (GetOwner() && GetOwner()->HasAuthority())
        {
            MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronCombatBridge, CurrentCombatLayer, this);
   }    
    }
}

// ========== STATUS EFFECTS SYSTEM IMPLEMENTATION ==========

void UAuracronCombatBridge::UpdateStatusEffects(float DeltaTime)
{
    if (!GetWorld())
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Atualizar status effects ativos
    for (int32 i = ActiveStatusEffects.Num() - 1; i >= 0; i--)
    {
        FAuracronActiveStatusEffect& ActiveEffect = ActiveStatusEffects[i];
        
        // Pular se pausado
        if (ActiveEffect.bIsPaused)
        {
            continue;
        }
        
        // Atualizar duração restante
        if (ActiveEffect.Effect.Duration > 0.0f)
        {
            ActiveEffect.RemainingDuration -= DeltaTime;
            
            // Remover se expirou
            if (ActiveEffect.RemainingDuration <= 0.0f)
            {
                UE_LOG(LogAuracronCombat, Log, TEXT("Status effect %s expired"), *ActiveEffect.Effect.EffectName);
                RemoveStatusEffectVFX(ActiveEffect);
                ActiveStatusEffects.RemoveAt(i);
                continue;
            }
        }
        
        // Processar tick se necessário
        if (ActiveEffect.Effect.TickInterval > 0.0f)
        {
            if (CurrentTime - ActiveEffect.LastTickTime >= ActiveEffect.Effect.TickInterval)
            {
                ProcessStatusEffectTick(ActiveEffect, CurrentTime);
                ActiveEffect.LastTickTime = CurrentTime;
            }
        }
    }
}

void UAuracronCombatBridge::ProcessStatusEffectTick(FAuracronActiveStatusEffect& ActiveEffect, float CurrentTime)
{
    if (ActiveEffect.Effect.TickValue == 0.0f)
    {
        return;
    }
    
    AActor* Owner = GetOwner();
    if (!Owner)
    {
        return;
    }
    
    float TickValue = ActiveEffect.Effect.TickValue * ActiveEffect.CurrentStacks;
    
    // Aplicar efeito baseado no tipo
    switch (ActiveEffect.Effect.StatusType)
    {
        case EAuracronStatusEffectType::Poison:
        case EAuracronStatusEffectType::Burn:
        {
            // Aplicar dano ao longo do tempo
            if (TickValue < 0.0f)
            {
                // Implementar dano (integração com sistema de saúde)
                UE_LOG(LogAuracronCombat, Log, TEXT("DoT effect %s dealt %.2f damage"), 
                    *ActiveEffect.Effect.EffectName, FMath::Abs(TickValue));
            }
            break;
        }
        case EAuracronStatusEffectType::HealthRegeneration:
        {
            // Aplicar cura ao longo do tempo
            if (TickValue > 0.0f)
            {
                // Implementar cura (integração com sistema de saúde)
                UE_LOG(LogAuracronCombat, Log, TEXT("HoT effect %s healed %.2f health"), 
                    *ActiveEffect.Effect.EffectName, TickValue);
            }
            break;
        }
        case EAuracronStatusEffectType::ManaRegeneration:
        {
            // Aplicar regeneração de mana
            if (TickValue > 0.0f)
            {
                // Implementar regeneração de mana
                UE_LOG(LogAuracronCombat, Log, TEXT("Mana regen effect %s restored %.2f mana"), 
                    *ActiveEffect.Effect.EffectName, TickValue);
            }
            break;
        }
        default:
            break;
    }
}

bool UAuracronCombatBridge::ApplyStatusEffect(const FAuracronStatusEffect& StatusEffect, AActor* Source)
{
    if (!CanApplyStatusEffect(StatusEffect))
    {
        return false;
    }
    
    // Verificar se já existe um efeito com o mesmo nome
    FAuracronActiveStatusEffect* ExistingEffect = FindActiveStatusEffect(StatusEffect.EffectName);
    
    if (ExistingEffect)
    {
        if (StatusEffect.bCanStack)
        {
            // Adicionar stacks se possível
            if (ExistingEffect->CurrentStacks < StatusEffect.MaxStacks)
            {
                ExistingEffect->CurrentStacks++;
                ExistingEffect->RemainingDuration = StatusEffect.Duration; // Renovar duração
                UE_LOG(LogAuracronCombat, Log, TEXT("Status effect %s stacked to %d"), 
                    *StatusEffect.EffectName, ExistingEffect->CurrentStacks);
                return true;
            }
            else
            {
                // Apenas renovar duração se já no máximo de stacks
                ExistingEffect->RemainingDuration = StatusEffect.Duration;
                return true;
            }
        }
        else
        {
            // Renovar duração do efeito existente
            ExistingEffect->RemainingDuration = StatusEffect.Duration;
            return true;
        }
    }
    
    // Verificar limite de status effects
    if (ActiveStatusEffects.Num() >= MaxActiveStatusEffects)
    {
        UE_LOG(LogAuracronCombat, Warning, TEXT("Cannot apply status effect %s: maximum limit reached"), 
            *StatusEffect.EffectName);
        return false;
    }
    
    // Criar novo status effect ativo
    FAuracronActiveStatusEffect NewActiveEffect(StatusEffect, Source);
    NewActiveEffect.StartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    NewActiveEffect.LastTickTime = NewActiveEffect.StartTime;
    
    ActiveStatusEffects.Add(NewActiveEffect);
    
    // Aplicar efeitos visuais
    if (bAutoApplyVFX)
    {
        ApplyStatusEffectVFX(NewActiveEffect);
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Applied status effect: %s (Duration: %.2f, Magnitude: %.2f)"), 
        *StatusEffect.EffectName, StatusEffect.Duration, StatusEffect.Magnitude);
    
    return true;
}

bool UAuracronCombatBridge::ApplyStatusEffectByType(EAuracronStatusEffectType StatusType, float Duration, float Magnitude, AActor* Source)
{
    if (!StatusEffectTemplates.Contains(StatusType))
    {
        UE_LOG(LogAuracronCombat, Warning, TEXT("No template found for status effect type: %d"), (int32)StatusType);
        return false;
    }
    
    FAuracronStatusEffect EffectToApply = StatusEffectTemplates[StatusType];
    
    // Sobrescrever duração e magnitude se especificados
    if (Duration >= 0.0f)
    {
        EffectToApply.Duration = Duration;
    }
    if (Magnitude != 1.0f)
    {
        EffectToApply.Magnitude = Magnitude;
    }
    
    return ApplyStatusEffect(EffectToApply, Source);
}

bool UAuracronCombatBridge::RemoveStatusEffect(const FString& EffectID)
{
    for (int32 i = 0; i < ActiveStatusEffects.Num(); i++)
    {
        if (ActiveStatusEffects[i].EffectID == EffectID)
        {
            RemoveStatusEffectVFX(ActiveStatusEffects[i]);
            ActiveStatusEffects.RemoveAt(i);
            UE_LOG(LogAuracronCombat, Log, TEXT("Removed status effect with ID: %s"), *EffectID);
            return true;
        }
    }
    
    return false;
}

int32 UAuracronCombatBridge::RemoveStatusEffectsByName(const FString& EffectName)
{
    int32 RemovedCount = 0;
    
    for (int32 i = ActiveStatusEffects.Num() - 1; i >= 0; i--)
    {
        if (ActiveStatusEffects[i].Effect.EffectName == EffectName)
        {
            RemoveStatusEffectVFX(ActiveStatusEffects[i]);
            ActiveStatusEffects.RemoveAt(i);
            RemovedCount++;
        }
    }
    
    if (RemovedCount > 0)
    {
        UE_LOG(LogAuracronCombat, Log, TEXT("Removed %d status effects named: %s"), RemovedCount, *EffectName);
    }
    
    return RemovedCount;
}

int32 UAuracronCombatBridge::RemoveStatusEffectsByType(EAuracronStatusEffectType StatusType)
{
    int32 RemovedCount = 0;
    
    for (int32 i = ActiveStatusEffects.Num() - 1; i >= 0; i--)
    {
        if (ActiveStatusEffects[i].Effect.StatusType == StatusType)
        {
            RemoveStatusEffectVFX(ActiveStatusEffects[i]);
            ActiveStatusEffects.RemoveAt(i);
            RemovedCount++;
        }
    }
    
    if (RemovedCount > 0)
    {
        UE_LOG(LogAuracronCombat, Log, TEXT("Removed %d status effects of type: %d"), RemovedCount, (int32)StatusType);
    }
    
    return RemovedCount;
}

int32 UAuracronCombatBridge::RemoveStatusEffectsByCategory(EAuracronStatusCategory Category)
{
    int32 RemovedCount = 0;
    
    for (int32 i = ActiveStatusEffects.Num() - 1; i >= 0; i--)
    {
        if (ActiveStatusEffects[i].Effect.Category == Category)
        {
            RemoveStatusEffectVFX(ActiveStatusEffects[i]);
            ActiveStatusEffects.RemoveAt(i);
            RemovedCount++;
        }
    }
    
    if (RemovedCount > 0)
    {
        UE_LOG(LogAuracronCombat, Log, TEXT("Removed %d status effects of category: %d"), RemovedCount, (int32)Category);
    }
    
    return RemovedCount;
}

void UAuracronCombatBridge::ClearAllStatusEffects()
{
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        RemoveStatusEffectVFX(ActiveEffect);
    }
    
    int32 ClearedCount = ActiveStatusEffects.Num();
    ActiveStatusEffects.Empty();
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Cleared %d status effects"), ClearedCount);
}

bool UAuracronCombatBridge::CanTargetLayer(EAuracronCombatLayer TargetLayer) const
{
    if (const FAuracronLayerCombatData* LayerData = LayerCombatData.Find(CurrentCombatLayer))
    {
        return LayerData->bCanTargetOtherLayers && LayerData->TargetableLayers.Contains(TargetLayer);
    }
    
    // Por padrão, só pode atacar a mesma camada
    return CurrentCombatLayer == TargetLayer;
}

float UAuracronCombatBridge::CalculateLayerDamageModifier(AActor* Target) const
{
    if (!Target)
    {
        return 1.0f;
    }
    
    // Tentar obter o combat bridge do alvo
    if (UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        EAuracronCombatLayer TargetLayer = TargetCombatBridge->GetCurrentCombatLayer();
        
        if (const FAuracronLayerCombatData* LayerData = LayerCombatData.Find(CurrentCombatLayer))
        {
            // Mesmo layer = modificador normal
            if (CurrentCombatLayer == TargetLayer)
            {
                return LayerData->DamageMultiplier;
            }
            
            // Layers diferentes = redução de dano se permitido
            if (CanTargetLayer(TargetLayer))
            {
                return LayerData->DamageMultiplier * 0.7f; // 30% de redução para ataques entre camadas
            }
            
            // Não pode atacar = sem dano
            return 0.0f;
        }
    }
    
    return 1.0f;
}

// === Integration with Existing Systems ===

void UAuracronCombatBridge::ApplySigilCombatModifiers()
{
    if (SigilosBridge)
    {
        // Aplicar modificadores de combate dos sígilos
        SigilosBridge->ApplyCombatModifiersPublic();
        
        // Verificar se há efeitos de maestria ativos
        float MasteryLevel = SigilosBridge->GetOverallMasteryPublic();
        if (MasteryLevel > 0.0f)
        {
            // Aplicar bônus de maestria ao combate
            float MasteryBonus = 1.0f + (MasteryLevel * 0.1f); // 10% por nível de maestria
            UE_LOG(LogAuracronCombat, Log, TEXT("Applying mastery bonus %.2f for %s"), 
                   MasteryBonus, GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
        }
        
        UE_LOG(LogAuracronCombat, Log, TEXT("Sigil combat modifiers applied for %s"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

void UAuracronCombatBridge::OnVerticalTransition(EAuracronRealmType FromRealm, EAuracronRealmType ToRealm)
{
    // Mapear realms para camadas de combate
    EAuracronCombatLayer NewLayer = EAuracronCombatLayer::Surface;
    
    switch (ToRealm)
    {
        case EAuracronRealmType::PlanicieRadiante:
            NewLayer = EAuracronCombatLayer::Surface;
            break;
        case EAuracronRealmType::FirmamentoZephyr:
            NewLayer = EAuracronCombatLayer::Sky;
            break;
        case EAuracronRealmType::AbismoUmbrio:
            NewLayer = EAuracronCombatLayer::Underground;
            break;
    }
    
    SetCombatLayer(NewLayer);
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s transitioned from realm %d to %d, new combat layer: %d"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"), 
           (int32)FromRealm, (int32)ToRealm, (int32)NewLayer);
}

void UAuracronCombatBridge::ApplyRealmCombatBonuses()
{
    if (RealmsBridge)
    {
        // Integrar com os bônus específicos de cada realm
        // O RealmsBridge já tem os multiplicadores implementados
        UE_LOG(LogAuracronCombat, Log, TEXT("Applying realm combat bonuses for %s"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

// === Combat Interface Implementation ===

void UAuracronCombatBridge::OnCombatStarted_Implementation(AActor* Instigator, AActor* Target)
{
    bInCombat = true;
    CombatStartTime = GetWorld()->GetTimeSeconds();
    
    // Aplicar modificadores de combate
    ApplySigilCombatModifiers();
    
    // Integrar com sistema de sígilos
    if (SigilosBridge)
    {
        SigilosBridge->OnCombatStartPublic();
        UE_LOG(LogAuracronCombat, Log, TEXT("Sigil combat modifiers applied for %s"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Combat started for %s - Instigator: %s, Target: %s"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
           Instigator ? *Instigator->GetName() : TEXT("Unknown"),
           Target ? *Target->GetName() : TEXT("Unknown"));
    
    // Trigger replication
    if (GetOwner() && GetOwner()->HasAuthority())
    {
        MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronCombatBridge, bInCombat, this);
    }
}

void UAuracronCombatBridge::OnCombatEnded_Implementation(const TArray<AActor*>& Participants)
{
    float CombatDuration = GetWorld()->GetTimeSeconds() - CombatStartTime;
    bInCombat = false;
    
    // Integrar com sistema de sígilos
    if (SigilosBridge)
    {
        SigilosBridge->OnCombatEndPublic();
        UE_LOG(LogAuracronCombat, Log, TEXT("Sigil combat effects removed for %s"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Combat ended for %s after %.2f seconds"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"), 
           CombatDuration);
    
    // Trigger replication
    if (GetOwner() && GetOwner()->HasAuthority())
    {
        MARK_PROPERTY_DIRTY_FROM_NAME(UAuracronCombatBridge, bInCombat, this);
    }
}

void UAuracronCombatBridge::OnSigilDamageDealt_Implementation(AActor* DamageDealer, AActor* DamageReceiver, float DamageAmount, const FString& SigilType)
{
    // Calcular modificadores de camada para o dano de sigilo
    float LayerModifier = CalculateLayerDamageModifier(DamageReceiver);
    float FinalDamage = DamageAmount * LayerModifier;
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Sigil damage dealt: %s -> %s, Amount: %.2f (modified: %.2f), Type: %s"), 
           DamageDealer ? *DamageDealer->GetName() : TEXT("Unknown"),
           DamageReceiver ? *DamageReceiver->GetName() : TEXT("Unknown"),
           DamageAmount, FinalDamage, *SigilType);
}

// === Targeting System ===

TArray<AActor*> UAuracronCombatBridge::FindValidTargetsInLayers(float Range, bool bIncludeOtherLayers) const
{
    TArray<AActor*> ValidTargets;
    
    if (!GetOwner())
    {
        return ValidTargets;
    }
    
    // Usar cache se disponível
    if (GetWorld()->GetTimeSeconds() - LastTargetCacheTime < TARGET_CACHE_DURATION && !CachedValidTargets.IsEmpty())
    {
        for (const TWeakObjectPtr<AActor>& WeakTarget : CachedValidTargets)
        {
            if (AActor* Target = WeakTarget.Get())
            {
                ValidTargets.Add(Target);
            }
        }
        return ValidTargets;
    }
    
    // Encontrar todos os atores em alcance
    TArray<AActor*> OverlappingActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), APawn::StaticClass(), OverlappingActors);
    
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    
    for (AActor* Actor : OverlappingActors)
    {
        if (!Actor || Actor == GetOwner())
        {
            continue;
        }
        
        // Verificar se está em alcance
        if (!IsTargetInLayerRange(Actor, Range))
        {
            continue;
        }
        
        // Verificar camada se necessário
        if (!bIncludeOtherLayers)
        {
            if (UAuracronCombatBridge* TargetCombatBridge = Actor->FindComponentByClass<UAuracronCombatBridge>())
            {
                if (TargetCombatBridge->GetCurrentCombatLayer() != CurrentCombatLayer)
                {
                    continue;
                }
            }
        }
        
        ValidTargets.Add(Actor);
    }
    
    // Atualizar cache
    CachedValidTargets.Empty();
    for (AActor* Target : ValidTargets)
    {
        CachedValidTargets.Add(Target);
    }
    LastTargetCacheTime = GetWorld()->GetTimeSeconds();
    
    return ValidTargets;
}

float UAuracronCombatBridge::CalculateEffectiveRange(float BaseRange, EAuracronCombatLayer TargetLayer) const
{
    if (const FAuracronLayerCombatData* LayerData = LayerCombatData.Find(CurrentCombatLayer))
    {
        float RangeMultiplier = LayerData->RangeMultiplier;
        
        // Reduzir alcance para ataques entre camadas
        if (CurrentCombatLayer != TargetLayer)
        {
            RangeMultiplier *= 0.8f; // 20% de redução
        }
        
        return BaseRange * RangeMultiplier;
    }
    
    return BaseRange;
}

// === Damage Calculation ===

float UAuracronCombatBridge::CalculateFinalDamage(float BaseDamage, AActor* Target, const FString& DamageType) const
{
    // Usar o novo sistema de dano adaptativo
    float FinalDamage = CalculateAdaptiveDamage(BaseDamage, Target, DamageType);
    
    // Integrar com modificadores de sígilos (se disponível)
    if (SigilosBridge)
    {
        // O SigilosBridge já tem toda a lógica de modificadores implementada
        // Aqui apenas aplicamos um multiplicador base
        // Aplicar modificadores específicos de sígilos se disponível
    if (SigilosBridge)
    {
        // Obter modificadores dinâmicos baseados no sigilo ativo
        float SigilModifier = 1.0f;
        if (SigilosBridge->GetIsInCombat())
        {
            // Aplicar modificadores de combate específicos do sigilo
            // Verificar se há sígilos ativos baseado no tipo selecionado
            bool bHasActiveSigil = SigilosBridge->SelectedSigiloType != EAuracronSigiloType::None;
            SigilModifier = bHasActiveSigil ? 1.2f : 1.0f;
        }
        FinalDamage *= SigilModifier;
    }
    }
    
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Final damage calculation: Base=%.2f, Adaptive=%.2f"), 
           BaseDamage, FinalDamage);
    
    return FinalDamage;
}

void UAuracronCombatBridge::ApplyAreaEffect(FVector Location, float Radius, float Damage, bool bAffectAllLayers)
{
    TArray<AActor*> AffectedActors;
    
    if (bAffectAllLayers)
    {
        AffectedActors = FindValidTargetsInLayers(Radius, true);
    }
    else
    {
        AffectedActors = FindValidTargetsInLayers(Radius, false);
    }
    
    for (AActor* Actor : AffectedActors)
    {
        float Distance = FVector::Dist(Location, Actor->GetActorLocation());
        if (Distance <= Radius)
        {
            // Calcular dano baseado na distância
            float DistanceMultiplier = 1.0f - (Distance / Radius);
            float FinalDamage = CalculateFinalDamage(Damage * DistanceMultiplier, Actor);
            
            // Aplicar dano (integração com sistema de dano existente)
            UE_LOG(LogAuracronCombat, Log, TEXT("Area effect damage: %s received %.2f damage"), 
                   *Actor->GetName(), FinalDamage);
        }
    }
}

// === Internal Methods ===

void UAuracronCombatBridge::InitializeBridgeReferences()
{
    if (AActor* Owner = GetOwner())
    {
        // Encontrar referências aos outros bridges
        SigilosBridge = Owner->FindComponentByClass<UAuracronSigilosBridge>();
        VerticalTransitionsBridge = Owner->FindComponentByClass<UAuracronVerticalTransitionsBridge>();
        RealmsBridge = Owner->FindComponentByClass<UAuracronRealmsBridge>();
        AbilitySystemComponent = Owner->FindComponentByClass<UAbilitySystemComponent>();
        
        UE_LOG(LogAuracronCombat, Log, TEXT("Bridge references initialized - Sigilos: %s, Transitions: %s, Realms: %s, ASC: %s"),
               SigilosBridge ? TEXT("Found") : TEXT("Not Found"),
               VerticalTransitionsBridge ? TEXT("Found") : TEXT("Not Found"),
               RealmsBridge ? TEXT("Found") : TEXT("Not Found"),
               AbilitySystemComponent ? TEXT("Found") : TEXT("Not Found"));
    }
}

void UAuracronCombatBridge::SetupDefaultLayerData()
{
    // Surface Layer - Balanced
    FAuracronLayerCombatData SurfaceData;
    SurfaceData.Layer = EAuracronCombatLayer::Surface;
    SurfaceData.DamageMultiplier = 1.0f;
    SurfaceData.RangeMultiplier = 1.0f;
    SurfaceData.AreaOfEffectMultiplier = 1.0f;
    SurfaceData.bCanTargetOtherLayers = true;
    SurfaceData.TargetableLayers = {EAuracronCombatLayer::Sky, EAuracronCombatLayer::Underground};
    LayerCombatData.Add(EAuracronCombatLayer::Surface, SurfaceData);
    
    // Sky Layer - High range, lower damage
    FAuracronLayerCombatData SkyData;
    SkyData.Layer = EAuracronCombatLayer::Sky;
    SkyData.DamageMultiplier = 0.9f;
    SkyData.RangeMultiplier = 1.5f;
    SkyData.AreaOfEffectMultiplier = 1.2f;
    SkyData.bCanTargetOtherLayers = true;
    SkyData.TargetableLayers = {EAuracronCombatLayer::Surface};
    LayerCombatData.Add(EAuracronCombatLayer::Sky, SkyData);
    
    // Underground Layer - High damage, lower range
    FAuracronLayerCombatData UndergroundData;
    UndergroundData.Layer = EAuracronCombatLayer::Underground;
    UndergroundData.DamageMultiplier = 1.2f;
    UndergroundData.RangeMultiplier = 0.8f;
    UndergroundData.AreaOfEffectMultiplier = 0.9f;
    UndergroundData.bCanTargetOtherLayers = true;
    UndergroundData.TargetableLayers = {EAuracronCombatLayer::Surface};
    LayerCombatData.Add(EAuracronCombatLayer::Underground, UndergroundData);
}

void UAuracronCombatBridge::UpdateLayerModifiers()
{
    // Atualizar modificadores baseado na camada atual
    // Esta função é chamada quando a camada muda ou durante o combate
    
    if (const FAuracronLayerCombatData* LayerData = LayerCombatData.Find(CurrentCombatLayer))
    {
        // Aplicar modificadores através do Ability System se disponível
        if (AbilitySystemComponent)
        {
            // Integração com GameplayEffects para modificadores de camada
            // O sistema já está preparado para isso através do ASC
        }
    }
}

void UAuracronCombatBridge::OnRep_CurrentCombatLayer()
{
    // Chamado quando a camada é replicada
    UpdateLayerModifiers();
    ApplyRealmCombatBonuses();
    
    // Aplicar efeitos automáticos da nova camada
    ApplyAutomaticLayerEffects();
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s combat layer replicated to: %d"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"), 
           (int32)CurrentCombatLayer);
}

bool UAuracronCombatBridge::IsTargetInLayerRange(AActor* Target, float Range) const
{
    if (!Target || !GetOwner())
    {
        return false;
    }
    
    float Distance = CalculateLayerDistance(Target);
    
    // Obter alcance efetivo baseado na camada do alvo
    EAuracronCombatLayer TargetLayer = EAuracronCombatLayer::Surface;
    if (UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        TargetLayer = TargetCombatBridge->GetCurrentCombatLayer();
    }
    
    float EffectiveRange = CalculateEffectiveRange(Range, TargetLayer);
    
    return Distance <= EffectiveRange;
}

float UAuracronCombatBridge::CalculateLayerDistance(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return FLT_MAX;
    }
    
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    
    // Calcular distância 3D considerando as camadas
    float HorizontalDistance = FVector2D::Distance(
        FVector2D(OwnerLocation.X, OwnerLocation.Y),
        FVector2D(TargetLocation.X, TargetLocation.Y)
    );
    
    float VerticalDistance = FMath::Abs(OwnerLocation.Z - TargetLocation.Z);
    
    // Verificar se estão na mesma camada
    EAuracronCombatLayer TargetLayer = EAuracronCombatLayer::Surface;
    if (UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        TargetLayer = TargetCombatBridge->GetCurrentCombatLayer();
    }
    
    // Se estão em camadas diferentes, aumentar a distância efetiva
    if (CurrentCombatLayer != TargetLayer)
    {
        VerticalDistance *= 1.5f; // Penalidade por estar em camada diferente
    }
    
    return FMath::Sqrt(HorizontalDistance * HorizontalDistance + VerticalDistance * VerticalDistance);
}

// === Adaptive Damage System Implementation ===

float UAuracronCombatBridge::CalculateAdaptiveDamage(float BaseDamage, AActor* Target, const FString& DamageType) const
{
    float AdaptiveDamage = BaseDamage;
    
    // Aplicar modificador de camada base
    AdaptiveDamage *= CalculateLayerDamageModifier(Target);
    
    // Aplicar modificador posicional
    AdaptiveDamage *= CalculatePositionalModifier(Target);
    
    // Aplicar vantagem de elevação
    AdaptiveDamage *= (1.0f + CalculateElevationAdvantage(Target));
    
    // Aplicar resistência adaptativa do alvo
    if (UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        float Resistance = TargetCombatBridge->CalculateAdaptiveResistance(DamageType);
        AdaptiveDamage *= (1.0f - Resistance);
    }
    
    // Aplicar multiplicador de combo
    AdaptiveDamage *= CurrentComboMultiplier;
    
    // Aplicar modificadores de efeitos de camada
    float LayerEffectModifier = GetLayerEffectDamageModifier();
    AdaptiveDamage *= LayerEffectModifier;
    
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Adaptive damage calculation: Base=%.2f, Final=%.2f, Combo=%.2f, LayerEffects=%.2f"), 
           BaseDamage, AdaptiveDamage, CurrentComboMultiplier, LayerEffectModifier);
    
    return AdaptiveDamage;
}

float UAuracronCombatBridge::CalculatePositionalModifier(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return 1.0f;
    }
    
    float PositionalModifier = 1.0f;
    
    // Calcular bônus de flanqueamento
    PositionalModifier += CalculateFlankingBonus(Target);
    
    // Verificar se alvo está em posição vulnerável
    if (IsTargetInVulnerablePosition(Target))
    {
        PositionalModifier += 0.15f; // 15% de bônus para alvos vulneráveis
    }
    
    return PositionalModifier;
}

float UAuracronCombatBridge::CalculateComboMultiplier(const FString& AttackSequence) const
{
    if (!GetWorld())
    {
        return 1.0f;
    }
    
    // Verificar se o combo foi resetado
    if (GetWorld()->GetTimeSeconds() - LastAttackTime > COMBO_RESET_TIME)
    {
        return 1.0f;
    }
    
    // Calcular multiplicador baseado no número de ataques consecutivos
    float BaseMultiplier = 1.0f + (ConsecutiveAttacks * 0.1f);
    BaseMultiplier = FMath::Min(BaseMultiplier, MAX_COMBO_MULTIPLIER);
    
    // Verificar se há combo especial ativo
    FString DetectedCombo = DetectSpecialCombo();
    if (!DetectedCombo.IsEmpty())
    {
        float SpecialMultiplier = CalculateSpecialComboMultiplier(DetectedCombo);
        
        // Usar o maior multiplicador entre base e especial
        return FMath::Max(BaseMultiplier, SpecialMultiplier);
    }
    
    return BaseMultiplier;
}

float UAuracronCombatBridge::CalculateElevationAdvantage(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return 0.0f;
    }
    
    float OwnerZ = GetOwner()->GetActorLocation().Z;
    float TargetZ = Target->GetActorLocation().Z;
    float HeightDifference = OwnerZ - TargetZ;
    
    // Apenas aplicar bônus se estivermos acima do alvo
    if (HeightDifference <= 0.0f)
    {
        return 0.0f;
    }
    
    // Calcular bônus baseado na diferença de altura
    float ElevationBonus = HeightDifference * ELEVATION_BONUS_PER_METER;
    
    // Aplicar limite máximo
    ElevationBonus = FMath::Min(ElevationBonus, MAX_ELEVATION_BONUS);
    
    return ElevationBonus;
}

float UAuracronCombatBridge::CalculateAdaptiveResistance(const FString& DamageType) const
{
    if (DamageType.IsEmpty())
    {
        return 0.0f;
    }
    
    // Buscar resistência acumulada para este tipo de dano
    if (const float* Resistance = DamageTypeResistance.Find(DamageType))
    {
        return FMath::Min(*Resistance, MAX_RESISTANCE);
    }
    
    return 0.0f;
}

void UAuracronCombatBridge::RegisterDamageReceived(float Damage, const FString& DamageType, AActor* Source)
{
    // Atualizar total de dano recebido
    TotalDamageReceived += Damage;
    
    // Atualizar resistência para este tipo de dano
    if (!DamageType.IsEmpty())
    {
        float& CurrentResistance = DamageTypeResistance.FindOrAdd(DamageType, 0.0f);
        CurrentResistance += Damage * RESISTANCE_BUILDUP_RATE;
        CurrentResistance = FMath::Min(CurrentResistance, MAX_RESISTANCE);
    }
    
    // Registrar dano por fonte
    if (Source)
    {
        float& DamageFromSource = DamageReceivedFromSources.FindOrAdd(Source, 0.0f);
        DamageFromSource += Damage;
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s received %.2f %s damage from %s. Total: %.2f"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
           Damage, *DamageType, 
           Source ? *Source->GetName() : TEXT("Unknown"),
           TotalDamageReceived);
}

void UAuracronCombatBridge::RegisterDamageDealt(float Damage, const FString& DamageType, AActor* Target)
{
    // Atualizar total de dano causado
    TotalDamageDealt += Damage;
    
    // Atualizar sistema de combo
    ConsecutiveAttacks++;
    LastAttackTime = GetWorld()->GetTimeSeconds();
    
    // Adicionar à sequência de ataques
    if (!DamageType.IsEmpty())
    {
        CurrentAttackSequence.Add(DamageType);
        
        // Limitar tamanho da sequência
        if (CurrentAttackSequence.Num() > MAX_COMBO_SEQUENCE_LENGTH)
        {
            CurrentAttackSequence.RemoveAt(0);
        }
    }
    
    // Detectar e ativar combos especiais
    FString DetectedCombo = DetectSpecialCombo();
    if (!DetectedCombo.IsEmpty() && DetectedCombo != ActiveSpecialCombo)
    {
        // Ativar novo combo especial
        ActiveSpecialCombo = DetectedCombo;
        SpecialComboTimeRemaining = SPECIAL_COMBO_DURATION;
        SpecialComboMultiplier = CalculateSpecialComboMultiplier(DetectedCombo);
        
        UE_LOG(LogAuracronCombat, Warning, TEXT("%s activated special combo: %s (%.2fx multiplier)"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
               *ActiveSpecialCombo, SpecialComboMultiplier);
    }
    
    // Atualizar multiplicador de combo
    CurrentComboMultiplier = CalculateComboMultiplier("");
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s dealt %.2f %s damage to %s. Combo: %dx (%.2f) %s"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
           Damage, *DamageType,
           Target ? *Target->GetName() : TEXT("Unknown"),
           ConsecutiveAttacks, CurrentComboMultiplier,
           !ActiveSpecialCombo.IsEmpty() ? *FString::Printf(TEXT("[%s]"), *ActiveSpecialCombo) : TEXT(""));
}

// === Internal Adaptive Methods ===

void UAuracronCombatBridge::UpdateAdaptiveResistances()
{
    // Decair resistências ao longo do tempo
    float DecayRate = 0.01f; // 1% por tick
    
    for (auto& ResistancePair : DamageTypeResistance)
    {
        ResistancePair.Value = FMath::Max(0.0f, ResistancePair.Value - DecayRate);
    }
    
    // Remover resistências que chegaram a zero
    for (auto It = DamageTypeResistance.CreateIterator(); It; ++It)
    {
        if (It.Value() <= 0.0f)
        {
            It.RemoveCurrent();
        }
    }
}

void UAuracronCombatBridge::CheckComboReset()
{
    if (GetWorld()->GetTimeSeconds() - LastAttackTime > COMBO_RESET_TIME)
    {
        ConsecutiveAttacks = 0;
        CurrentComboMultiplier = 1.0f;
        CurrentAttackSequence.Empty();
        
        UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("%s combo reset"), 
               GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
    }
}

float UAuracronCombatBridge::CalculateFlankingBonus(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return 0.0f;
    }
    
    // Obter direções
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    FVector TargetForward = Target->GetActorForwardVector();
    
    // Calcular vetor do alvo para o atacante
    FVector ToAttacker = (OwnerLocation - TargetLocation).GetSafeNormal();
    
    // Calcular ângulo entre a frente do alvo e a direção do atacante
    float DotProduct = FVector::DotProduct(TargetForward, ToAttacker);
    float Angle = FMath::RadiansToDegrees(FMath::Acos(DotProduct));
    
    // Se o ângulo for maior que o threshold, é flanqueamento
    if (Angle > FLANKING_ANGLE_THRESHOLD)
    {
        return FLANKING_DAMAGE_BONUS;
    }
    
    return 0.0f;
}

bool UAuracronCombatBridge::IsTargetInVulnerablePosition(AActor* Target) const
{
    if (!Target)
    {
        return false;
    }
    
    // Verificar se o alvo tem um CombatBridge
    UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>();
    if (!TargetCombatBridge)
    {
        return false;
    }
    
    // Alvo é vulnerável se:
    // 1. Recebeu muito dano recentemente
    // 2. Está em uma camada desvantajosa
    // 3. Não está em combate ativo
    
    bool bRecentlyDamaged = (GetWorld()->GetTimeSeconds() - TargetCombatBridge->LastAttackTime) < 1.0f;
    bool bInDisadvantageousLayer = (TargetCombatBridge->GetCurrentCombatLayer() == EAuracronCombatLayer::Underground && 
                                   CurrentCombatLayer == EAuracronCombatLayer::Sky);
    bool bNotInActiveCombat = !TargetCombatBridge->bInCombat;
    
    return bRecentlyDamaged || bInDisadvantageousLayer || bNotInActiveCombat;
}

// === Layer Effects System Implementation ===

bool UAuracronCombatBridge::ApplyLayerEffect(const FAuracronLayerEffect& Effect, AActor* Source)
{
    if (!CanApplyLayerEffect(Effect))
    {
        return false;
    }
    
    // Verifica se o efeito já existe
    FAuracronActiveLayerEffect* ExistingEffect = FindActiveLayerEffect(Effect.EffectName);
    
    if (ExistingEffect)
    {
        if (Effect.bStackable && ExistingEffect->CurrentStacks < Effect.MaxStacks)
        {
            // Adiciona stack
            ExistingEffect->CurrentStacks++;
            ExistingEffect->RemainingDuration = Effect.Duration; // Renova duração
            UE_LOG(LogAuracronCombat, Log, TEXT("Stacked layer effect %s to %d stacks"), *Effect.EffectName, ExistingEffect->CurrentStacks);
        }
        else
        {
            // Renova duração
            ExistingEffect->RemainingDuration = Effect.Duration;
            UE_LOG(LogAuracronCombat, Log, TEXT("Renewed layer effect %s duration"), *Effect.EffectName);
        }
    }
    else
    {
        // Adiciona novo efeito
        FAuracronActiveLayerEffect NewActiveEffect(Effect, Source);
        NewActiveEffect.StartTime = GetWorld()->GetTimeSeconds();
        ActiveLayerEffects.Add(NewActiveEffect);
        UE_LOG(LogAuracronCombat, Log, TEXT("Applied new layer effect %s"), *Effect.EffectName);
    }
    
    return true;
}

bool UAuracronCombatBridge::RemoveLayerEffect(const FString& EffectName)
{
    for (int32 i = ActiveLayerEffects.Num() - 1; i >= 0; i--)
    {
        if (ActiveLayerEffects[i].Effect.EffectName == EffectName)
        {
            ActiveLayerEffects.RemoveAt(i);
            UE_LOG(LogAuracronCombat, Log, TEXT("Removed layer effect %s"), *EffectName);
            return true;
        }
    }
    
    return false;
}

void UAuracronCombatBridge::RemoveLayerEffectsByType(EAuracronLayerEffectType EffectType)
{
    int32 RemovedCount = 0;
    
    for (int32 i = ActiveLayerEffects.Num() - 1; i >= 0; i--)
    {
        if (ActiveLayerEffects[i].Effect.EffectType == EffectType)
        {
            ActiveLayerEffects.RemoveAt(i);
            RemovedCount++;
        }
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Removed %d layer effects of type %d"), RemovedCount, (int32)EffectType);
}

TArray<FAuracronActiveLayerEffect> UAuracronCombatBridge::GetActiveLayerEffects() const
{
    return ActiveLayerEffects;
}

bool UAuracronCombatBridge::HasLayerEffect(const FString& EffectName) const
{
    for (const FAuracronActiveLayerEffect& ActiveEffect : ActiveLayerEffects)
    {
        if (ActiveEffect.Effect.EffectName == EffectName)
        {
            return true;
        }
    }
    
    return false;
}

float UAuracronCombatBridge::GetLayerEffectDamageModifier() const
{
    float TotalModifier = 1.0f;
    
    for (const FAuracronActiveLayerEffect& ActiveEffect : ActiveLayerEffects)
    {
        float EffectModifier = ActiveEffect.Effect.DamageModifier;
        
        // Aplica stacks se aplicável
        if (ActiveEffect.Effect.bStackable)
        {
            EffectModifier = 1.0f + ((EffectModifier - 1.0f) * ActiveEffect.CurrentStacks);
        }
        
        TotalModifier *= EffectModifier;
    }
    
    return TotalModifier;
}

float UAuracronCombatBridge::GetLayerEffectSpeedModifier() const
{
    float TotalModifier = 1.0f;
    
    for (const FAuracronActiveLayerEffect& ActiveEffect : ActiveLayerEffects)
    {
        float EffectModifier = ActiveEffect.Effect.SpeedModifier;
        
        // Aplica stacks se aplicável
        if (ActiveEffect.Effect.bStackable)
        {
            EffectModifier = 1.0f + ((EffectModifier - 1.0f) * ActiveEffect.CurrentStacks);
        }
        
        TotalModifier *= EffectModifier;
    }
    
    return TotalModifier;
}

float UAuracronCombatBridge::GetLayerEffectDefenseModifier() const
{
    float TotalModifier = 1.0f;
    
    for (const FAuracronActiveLayerEffect& ActiveEffect : ActiveLayerEffects)
    {
        float EffectModifier = ActiveEffect.Effect.DefenseModifier;
        
        // Aplica stacks se aplicável
        if (ActiveEffect.Effect.bStackable)
        {
            EffectModifier = 1.0f + ((EffectModifier - 1.0f) * ActiveEffect.CurrentStacks);
        }
        
        TotalModifier *= EffectModifier;
    }
    
    return TotalModifier;
}

void UAuracronCombatBridge::ApplyAutomaticLayerEffects()
{
    TArray<FAuracronLayerEffect>* LayerEffects = nullptr;
    
    switch (CurrentCombatLayer)
    {
        case EAuracronCombatLayer::Surface:
            LayerEffects = &SurfaceLayerEffects;
            break;
        case EAuracronCombatLayer::Sky:
            LayerEffects = &SkyLayerEffects;
            break;
        case EAuracronCombatLayer::Underground:
            LayerEffects = &UndergroundLayerEffects;
            break;
        default:
            break;
    }
    
    if (LayerEffects)
    {
        for (const FAuracronLayerEffect& Effect : *LayerEffects)
        {
            ApplyLayerEffect(Effect, GetOwner());
        }
        
        UE_LOG(LogAuracronCombat, Log, TEXT("Applied %d automatic effects for layer %d"), LayerEffects->Num(), (int32)CurrentCombatLayer);
    }
}

void UAuracronCombatBridge::ClearAllLayerEffects()
{
    int32 ClearedCount = ActiveLayerEffects.Num();
    ActiveLayerEffects.Empty();
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Cleared %d layer effects"), ClearedCount);
}

bool UAuracronCombatBridge::CanApplyLayerEffect(const FAuracronLayerEffect& Effect) const
{
    // Verificar se a camada alvo permite este efeito
    if (Effect.TargetLayer != EAuracronCombatLayer::Surface && 
        Effect.TargetLayer != CurrentCombatLayer)
    {
        return false;
    }
    
    // Verificar se já temos muitos efeitos ativos (máximo 20)
    if (ActiveLayerEffects.Num() >= 20)
    {
        return false;
    }
    
    // Verificar se o efeito já existe e não é stackable
    if (!Effect.bStackable && HasLayerEffect(Effect.EffectName))
    {
        return false;
    }
    
    return true;
}

FAuracronActiveLayerEffect* UAuracronCombatBridge::FindActiveLayerEffect(const FString& EffectName)
{
    for (FAuracronActiveLayerEffect& ActiveEffect : ActiveLayerEffects)
    {
        if (ActiveEffect.Effect.EffectName == EffectName)
        {
            return &ActiveEffect;
        }
    }
    
    return nullptr;
}

void UAuracronCombatBridge::UpdateLayerEffects()
{
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Atualizar duração dos efeitos ativos
    for (int32 i = ActiveLayerEffects.Num() - 1; i >= 0; i--)
    {
        FAuracronActiveLayerEffect& ActiveEffect = ActiveLayerEffects[i];
        
        if (ActiveEffect.RemainingDuration > 0.0f)
        {
            float ElapsedTime = CurrentTime - ActiveEffect.StartTime;
            ActiveEffect.RemainingDuration = ActiveEffect.Effect.Duration - ElapsedTime;
            
            // Remover efeito se expirou
            if (ActiveEffect.RemainingDuration <= 0.0f)
            {
                UE_LOG(LogAuracronCombat, Log, TEXT("Layer effect %s expired"), *ActiveEffect.Effect.EffectName);
                ActiveLayerEffects.RemoveAt(i);
            }
        }
    }
}

void UAuracronCombatBridge::RemoveExpiredLayerEffects()
{
    UpdateLayerEffects();
}

float UAuracronCombatBridge::ApplyLayerEffectModifiers(float BaseDamage, const FString& DamageType) const
{
    float ModifiedDamage = BaseDamage;
    
    // Aplicar modificador de dano dos efeitos
    ModifiedDamage *= GetLayerEffectDamageModifier();
    
    return ModifiedDamage;
}

void UAuracronCombatBridge::InitializeDefaultLayerEffects()
{
    SetupDefaultLayerEffects();
}

void UAuracronCombatBridge::OnRep_ActiveLayerEffects()
{
    // Callback para quando os efeitos ativos são replicados
    UE_LOG(LogAuracronCombat, Log, TEXT("Active layer effects replicated: %d effects"), ActiveLayerEffects.Num());
}

void UAuracronCombatBridge::SetupDefaultLayerEffects()
{
    // Configurar efeitos automáticos para cada camada
    
    // Surface Layer - Efeito de equilíbrio
    FAuracronLayerEffect BalanceEffect;
    BalanceEffect.EffectName = TEXT("Surface Balance");
    BalanceEffect.EffectType = EAuracronLayerEffectType::Buff;
    BalanceEffect.TargetLayer = EAuracronCombatLayer::Surface;
    BalanceEffect.DamageModifier = 1.0f;
    BalanceEffect.SpeedModifier = 1.0f;
    BalanceEffect.DefenseModifier = 1.0f;
    BalanceEffect.Duration = -1.0f; // Permanente
    BalanceEffect.bStackable = false;
    SurfaceLayerEffects.Add(BalanceEffect);
    
    // Sky Layer - Efeito de agilidade
    FAuracronLayerEffect AgilityEffect;
    AgilityEffect.EffectName = TEXT("Sky Agility");
    AgilityEffect.EffectType = EAuracronLayerEffectType::Buff;
    AgilityEffect.TargetLayer = EAuracronCombatLayer::Sky;
    AgilityEffect.DamageModifier = 0.9f;
    AgilityEffect.SpeedModifier = 1.3f;
    AgilityEffect.DefenseModifier = 0.8f;
    AgilityEffect.Duration = -1.0f; // Permanente
    AgilityEffect.bStackable = false;
    SkyLayerEffects.Add(AgilityEffect);
    
    // Underground Layer - Efeito de força
    FAuracronLayerEffect StrengthEffect;
    StrengthEffect.EffectName = TEXT("Underground Strength");
    StrengthEffect.EffectType = EAuracronLayerEffectType::Buff;
    StrengthEffect.TargetLayer = EAuracronCombatLayer::Underground;
    StrengthEffect.DamageModifier = 1.2f;
    StrengthEffect.SpeedModifier = 0.8f;
    StrengthEffect.DefenseModifier = 1.1f;
    StrengthEffect.Duration = -1.0f; // Permanente
    StrengthEffect.bStackable = false;
    UndergroundLayerEffects.Add(StrengthEffect);
}

// === 3D Targeting System Implementation ===

TArray<AActor*> UAuracronCombatBridge::FindTargetsInRadius(float Radius, const TArray<EAuracronCombatLayer>& AllowedLayers, bool bRequireLineOfSight) const
{
    TArray<AActor*> FoundTargets;
    
    if (!GetOwner() || !GetWorld())
    {
        return FoundTargets;
    }
    
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    
    // Usar overlap sphere para encontrar atores próximos
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());
    
    bool bHit = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        OwnerLocation,
        FQuat::Identity,
        ECC_Pawn,
        FCollisionShape::MakeSphere(Radius),
        QueryParams
    );
    
    if (bHit)
    {
        for (const FOverlapResult& Result : OverlapResults)
        {
            AActor* Actor = Result.GetActor();
            if (IsValidTarget(Actor))
            {
                // Verificar camada se especificada
                if (!AllowedLayers.IsEmpty())
                {
                    EAuracronCombatLayer ActorLayer = GetActorCombatLayer(Actor);
                    if (!AllowedLayers.Contains(ActorLayer))
                    {
                        continue;
                    }
                }
                
                // Verificar linha de visão se necessário
                if (bRequireLineOfSight && !HasLineOfSight3D(Actor))
                {
                    continue;
                }
                
                FoundTargets.Add(Actor);
            }
        }
    }
    
    // Ordenar por prioridade
    SortTargetsByPriority(FoundTargets);
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Found %d targets in radius %.2f"), FoundTargets.Num(), Radius);
    
    return FoundTargets;
}

TArray<AActor*> UAuracronCombatBridge::FindTargetsInRadiusAllLayers(float Radius, bool bRequireLineOfSight) const
{
    TArray<EAuracronCombatLayer> AllLayers;
    AllLayers.Add(EAuracronCombatLayer::Surface);
    AllLayers.Add(EAuracronCombatLayer::Sky);
    AllLayers.Add(EAuracronCombatLayer::Underground);
    
    return FindTargetsInRadius(Radius, AllLayers, bRequireLineOfSight);
}

AActor* UAuracronCombatBridge::FindBestTarget(float MaxRange, const TArray<EAuracronCombatLayer>& PreferredLayers) const
{
    TArray<AActor*> PotentialTargets = FindTargetsInRadius(MaxRange, PreferredLayers, true);
    
    if (PotentialTargets.IsEmpty())
    {
        return nullptr;
    }
    
    // O primeiro alvo já é o melhor devido à ordenação por prioridade
    AActor* BestTarget = PotentialTargets[0];
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Best target found: %s"), BestTarget ? *BestTarget->GetName() : TEXT("None"));
    
    return BestTarget;
}

AActor* UAuracronCombatBridge::FindBestTargetAllLayers(float MaxRange) const
{
    TArray<EAuracronCombatLayer> AllLayers;
    AllLayers.Add(EAuracronCombatLayer::Surface);
    AllLayers.Add(EAuracronCombatLayer::Sky);
    AllLayers.Add(EAuracronCombatLayer::Underground);
    
    return FindBestTarget(MaxRange, AllLayers);
}

bool UAuracronCombatBridge::IsTargetInValidRange(AActor* Target, float& OutDistance) const
{
    if (!Target || !GetOwner())
    {
        OutDistance = 0.0f;
        return false;
    }
    
    OutDistance = CalculateLayerAwareDistance(Target);
    float MaxRange = GetMaxRangeForCurrentLayer();
    
    return OutDistance <= MaxRange;
}

float UAuracronCombatBridge::GetMaxRangeForCurrentLayer() const
{
    switch (CurrentCombatLayer)
    {
        case EAuracronCombatLayer::Surface:
            return SurfaceMaxRange;
        case EAuracronCombatLayer::Sky:
            return SkyMaxRange;
        case EAuracronCombatLayer::Underground:
            return UndergroundMaxRange;
        default:
            return SurfaceMaxRange;
    }
}

TArray<AActor*> UAuracronCombatBridge::FindTargetsInCone(float Range, float ConeAngle, const TArray<EAuracronCombatLayer>& AllowedLayers) const
{
    TArray<AActor*> ConeTargets;
    
    if (!GetOwner())
    {
        return ConeTargets;
    }
    
    // Primeiro encontrar todos os alvos no raio
    TArray<AActor*> RadiusTargets = FindTargetsInRadius(Range, AllowedLayers, false);
    
    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector OwnerForward = GetOwner()->GetActorForwardVector();
    
    float ConeAngleRad = FMath::DegreesToRadians(ConeAngle * 0.5f); // Metade do ângulo
    
    for (AActor* Target : RadiusTargets)
    {
        FVector ToTarget = (Target->GetActorLocation() - OwnerLocation).GetSafeNormal();
        float DotProduct = FVector::DotProduct(OwnerForward, ToTarget);
        float AngleToTarget = FMath::Acos(DotProduct);
        
        if (AngleToTarget <= ConeAngleRad)
        {
            ConeTargets.Add(Target);
        }
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Found %d targets in cone (Range: %.2f, Angle: %.2f)"), ConeTargets.Num(), Range, ConeAngle);
    
    return ConeTargets;
}

TArray<AActor*> UAuracronCombatBridge::FindTargetsInConeAllLayers(float Range, float ConeAngle) const
{
    TArray<EAuracronCombatLayer> AllLayers;
    AllLayers.Add(EAuracronCombatLayer::Surface);
    AllLayers.Add(EAuracronCombatLayer::Sky);
    AllLayers.Add(EAuracronCombatLayer::Underground);
    
    return FindTargetsInCone(Range, ConeAngle, AllLayers);
}

TArray<AActor*> UAuracronCombatBridge::FindTargetsInLayeredSphere(const TMap<EAuracronCombatLayer, float>& LayerRanges) const
{
    TArray<AActor*> LayeredTargets;
    
    for (const auto& LayerRange : LayerRanges)
    {
        EAuracronCombatLayer Layer = LayerRange.Key;
        float Range = LayerRange.Value;
        
        TArray<EAuracronCombatLayer> SingleLayer;
        SingleLayer.Add(Layer);
        
        TArray<AActor*> LayerTargets = this->FindTargetsInRadius(Range, SingleLayer, true);
        
        for (AActor* Target : LayerTargets)
        {
            LayeredTargets.AddUnique(Target);
        }
    }
    
    // Note: Sorting removed to maintain const correctness
    // LayeredTargets.Sort([this](const AActor& A, const AActor& B) {
    //     return CalculateTargetPriority(&A) > CalculateTargetPriority(&B);
    // });
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Found %d targets in layered sphere"), LayeredTargets.Num());
    
    return LayeredTargets;
}

bool UAuracronCombatBridge::HasLineOfSight3D(AActor* Target, bool bIgnoreVerticalObstacles) const
{
    if (!Target || !GetOwner() || !GetWorld())
    {
        return false;
    }
    
    FVector Start = GetOwner()->GetActorLocation();
    FVector End = Target->GetActorLocation();
    
    // Ajustar altura para verificação de linha de visão
    Start.Z += 50.0f; // Offset para olhos/centro do ator
    End.Z += 50.0f;
    
    if (bIgnoreVerticalObstacles)
    {
        // Verificar apenas obstáculos horizontais
        Start.Z = End.Z; // Igualar alturas
    }
    
    return PerformLineOfSightTrace(Start, End, Target);
}



float UAuracronCombatBridge::CalculateTargetPriority(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return 0.0f;
    }
    
    float Priority = 100.0f; // Base priority
    
    // Fator de distância (mais próximo = maior prioridade)
    float Distance = CalculateLayerAwareDistance(Target);
    float DistanceFactor = FMath::Clamp(1.0f - (Distance / GetMaxRangeForCurrentLayer()), 0.1f, 1.0f);
    Priority *= DistanceFactor;
    
    // Fator de camada (mesma camada = maior prioridade)
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (TargetLayer == CurrentCombatLayer)
    {
        Priority *= 1.5f; // Bônus para mesma camada
    }
    
    // Fator de linha de visão
    if (HasLineOfSight3D(Target))
    {
        Priority *= 1.2f; // Bônus para linha de visão clara
    }
    
    // Fator de saúde (alvos com menos vida = maior prioridade)
    if (UAuracronCombatBridge* TargetCombat = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        // Assumindo que existe um sistema de saúde
        // Priority *= (1.0f + (1.0f - HealthPercentage));
    }
    
    return Priority;
}

// === Internal 3D Targeting System Methods ===

void UAuracronCombatBridge::UpdateTargetCache()
{
    if (!GetWorld())
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastTargetCacheUpdate < TargetCacheUpdateInterval)
    {
        return;
    }

    LastTargetCacheUpdate = CurrentTime;
    LayerTargetCache.Empty();

    // Encontrar todos os atores alvejáveis no mundo
    for (TActorIterator<AActor> ActorIterator(GetWorld()); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (IsValidTarget(Actor))
        {
            EAuracronCombatLayer ActorLayer = GetActorCombatLayer(Actor);
            LayerTargetCache.FindOrAdd(ActorLayer).Add(Actor);
        }
    }
}

bool UAuracronCombatBridge::IsValidTarget(AActor* Target) const
{
    if (!Target || Target == GetOwner())
    {
        return false;
    }

    // Verificar se é um Pawn válido
    if (!Target->IsA<APawn>())
    {
        return false;
    }

    // Verificar se o alvo está vivo
    if (AAuracronCharacter* AuracronChar = Cast<AAuracronCharacter>(Target))
    {
        if (!AuracronChar->IsAlive())
        {
            return false;
        }
    }
    else if (APawn* TargetPawn = Cast<APawn>(Target))
    {
        // Para outros tipos de Pawn, verificar se tem sistema de saúde genérico
        // Por enquanto, assumimos que são válidos se não são AuracronCharacter
        // TODO: Implementar verificação de vida para outros tipos de Pawn
    }

    return true;
}

EAuracronCombatLayer UAuracronCombatBridge::GetActorCombatLayer(AActor* Actor) const
{
    if (!Actor)
    {
        return EAuracronCombatLayer::Surface;
    }

    // Verificar se o ator tem um componente de combate
    if (UAuracronCombatBridge* ActorCombatBridge = Actor->FindComponentByClass<UAuracronCombatBridge>())
    {
        return ActorCombatBridge->GetCurrentCombatLayer();
    }

    // Determinar camada baseada na posição Z
    float ActorZ = Actor->GetActorLocation().Z;
    
    if (ActorZ > 1000.0f)
    {
        return EAuracronCombatLayer::Sky;
    }
    else if (ActorZ < -500.0f)
    {
        return EAuracronCombatLayer::Underground;
    }
    
    return EAuracronCombatLayer::Surface;
}

float UAuracronCombatBridge::CalculateLayerAwareDistance(AActor* Target) const
{
    if (!Target || !GetOwner())
    {
        return FLT_MAX;
    }

    FVector OwnerLocation = GetOwner()->GetActorLocation();
    FVector TargetLocation = Target->GetActorLocation();
    
    float BaseDistance = FVector::Dist(OwnerLocation, TargetLocation);
    
    // Aplicar penalidade de distância para camadas diferentes
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (TargetLayer != CurrentCombatLayer)
    {
        BaseDistance *= 1.5f; // 50% de penalidade para camadas diferentes
    }
    
    return BaseDistance;
}

void UAuracronCombatBridge::SortTargetsByPriority(TArray<AActor*>& Targets) const
{
    Targets.Sort([this](const AActor& A, const AActor& B) {
        float PriorityA = CalculateTargetPriority(const_cast<AActor*>(&A));
        float PriorityB = CalculateTargetPriority(const_cast<AActor*>(&B));
        return PriorityA > PriorityB;
    });
}

bool UAuracronCombatBridge::PerformLineOfSightTrace(const FVector& Start, const FVector& End, AActor* IgnoreActor) const
{
    if (!GetWorld())
    {
        return false;
    }

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(GetOwner());
    if (IgnoreActor)
    {
        QueryParams.AddIgnoredActor(IgnoreActor);
    }
    QueryParams.bTraceComplex = false;
    
    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECollisionChannel::ECC_Visibility,
        QueryParams
    );
    
    // Se não houve hit, há linha de visão clara
    return !bHit;
}

void UAuracronCombatBridge::InitializeTargetingSystem()
{
    // Configurar alcances máximos por camada
    SurfaceMaxRange = 1500.0f;
    SkyMaxRange = 2000.0f;
    UndergroundMaxRange = 1000.0f;
    
    // Configurar intervalo de atualização do cache
    TargetCacheUpdateInterval = 0.5f; // Atualizar cache a cada 0.5 segundos
    LastTargetCacheUpdate = 0.0f;
    
    // Configurar classes alvejáveis padrão
    TargetableClasses.Empty();
    TargetableClasses.Add(APawn::StaticClass());
    
    // Limpar cache inicial
    LayerTargetCache.Empty();
    
    UE_LOG(LogAuracronCombat, Log, TEXT("3D Targeting System initialized for %s"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
}

// === Damage Type System Implementation ===

float UAuracronCombatBridge::CalculateAdaptiveDamageByType(float BaseDamage, AActor* Target, EAuracronDamageType DamageType) const
{
    if (DamageType == EAuracronDamageType::TrueDamage)
    {
        // True damage ignora resistências e modificadores
        return BaseDamage;
    }

    float AdaptiveDamage = BaseDamage;
    
    // Aplicar modificadores base do tipo de dano
    AdaptiveDamage = ApplyDamageTypeModifiers(AdaptiveDamage, DamageType);
    
    // Aplicar modificador de camada
    AdaptiveDamage *= CalculateLayerDamageModifier(Target);
    
    // Aplicar modificador posicional
    AdaptiveDamage *= CalculatePositionalModifier(Target);
    
    // Aplicar vantagem de elevação
    AdaptiveDamage *= (1.0f + CalculateElevationAdvantage(Target));
    
    // Aplicar resistência adaptativa do alvo
    if (UAuracronCombatBridge* TargetCombatBridge = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        float Resistance = TargetCombatBridge->CalculateAdaptiveResistanceByType(DamageType);
        AdaptiveDamage *= (1.0f - Resistance);
    }
    
    // Aplicar multiplicador de combo
    AdaptiveDamage *= CurrentComboMultiplier;
    
    // Aplicar modificadores de efeitos de camada
    AdaptiveDamage *= GetLayerEffectDamageModifier();
    
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Adaptive damage by type: Base=%.2f, Final=%.2f, Type=%s"), 
           BaseDamage, AdaptiveDamage, *DamageTypeToString(DamageType));
    
    return AdaptiveDamage;
}

float UAuracronCombatBridge::CalculateAdaptiveResistanceByType(EAuracronDamageType DamageType) const
{
    if (DamageType == EAuracronDamageType::TrueDamage)
    {
        return 0.0f; // True damage não pode ser resistido
    }

    // Buscar resistência acumulada para este tipo de dano
    if (const float* Resistance = AdaptiveResistances.Find(DamageType))
    {
        // Aplicar modificadores base do tipo
        if (const FAuracronDamageTypeModifiers* Modifiers = DamageTypeModifiers.Find(DamageType))
        {
            float FinalResistance = *Resistance + Modifiers->BaseResistance;
            return FMath::Min(FinalResistance, Modifiers->MaxResistance);
        }
        return FMath::Min(*Resistance, 0.5f); // Fallback para 50% máximo
    }
    
    // Retornar resistência base se não há resistência acumulada
    if (const FAuracronDamageTypeModifiers* Modifiers = DamageTypeModifiers.Find(DamageType))
    {
        return Modifiers->BaseResistance;
    }
    
    return 0.0f;
}

void UAuracronCombatBridge::RegisterDamageReceivedByType(float Damage, EAuracronDamageType DamageType, AActor* Source)
{
    if (DamageType == EAuracronDamageType::TrueDamage)
    {
        // True damage não gera resistência
        return;
    }

    // Atualizar total de dano recebido
    TotalDamageReceived += Damage;
    
    // Atualizar histórico por tipo
    float& DamageByType = DamageReceivedByType.FindOrAdd(DamageType, 0.0f);
    DamageByType += Damage;
    
    // Atualizar resistência adaptativa
    const FAuracronDamageTypeModifiers* Modifiers = DamageTypeModifiers.Find(DamageType);
    float BuildupRate = Modifiers ? Modifiers->ResistanceBuildupRate : 0.1f;
    float MaxResistance = Modifiers ? Modifiers->MaxResistance : 0.5f;
    
    float& CurrentResistance = AdaptiveResistances.FindOrAdd(DamageType, 0.0f);
    CurrentResistance += Damage * BuildupRate;
    CurrentResistance = FMath::Min(CurrentResistance, MaxResistance);
    
    // Registrar dano por fonte
    if (Source)
    {
        float& DamageFromSource = DamageReceivedFromSources.FindOrAdd(Source, 0.0f);
        DamageFromSource += Damage;
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s received %.2f %s damage from %s. Resistance: %.2f"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
           Damage, *DamageTypeToString(DamageType),
           Source ? *Source->GetName() : TEXT("Unknown"),
           CurrentResistance);
}

float UAuracronCombatBridge::ApplyDamageTypeModifiers(float BaseDamage, EAuracronDamageType DamageType) const
{
    if (const FAuracronDamageTypeModifiers* Modifiers = DamageTypeModifiers.Find(DamageType))
    {
        return BaseDamage * Modifiers->BaseMultiplier;
    }
    
    return BaseDamage; // Sem modificadores, retorna dano base
}

EAuracronDamageType UAuracronCombatBridge::StringToDamageType(const FString& DamageTypeString) const
{
    if (DamageTypeString.Equals(TEXT("Physical"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Physical;
    if (DamageTypeString.Equals(TEXT("Magical"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Magical;
    if (DamageTypeString.Equals(TEXT("Fire"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Fire;
    if (DamageTypeString.Equals(TEXT("Ice"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Ice;
    if (DamageTypeString.Equals(TEXT("Lightning"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Lightning;
    if (DamageTypeString.Equals(TEXT("Poison"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Poison;
    if (DamageTypeString.Equals(TEXT("Psychic"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::Psychic;
    if (DamageTypeString.Equals(TEXT("True"), ESearchCase::IgnoreCase))
        return EAuracronDamageType::TrueDamage;
    
    return EAuracronDamageType::Physical; // Default
}

FString UAuracronCombatBridge::DamageTypeToString(EAuracronDamageType DamageType) const
{
    switch (DamageType)
    {
        case EAuracronDamageType::Physical:   return TEXT("Physical");
        case EAuracronDamageType::Magical:    return TEXT("Magical");
        case EAuracronDamageType::Fire:       return TEXT("Fire");
        case EAuracronDamageType::Ice:        return TEXT("Ice");
        case EAuracronDamageType::Lightning:  return TEXT("Lightning");
        case EAuracronDamageType::Poison:     return TEXT("Poison");
        case EAuracronDamageType::Psychic:    return TEXT("Psychic");
        case EAuracronDamageType::TrueDamage: return TEXT("True");
        default:                              return TEXT("Physical");
    }
}

void UAuracronCombatBridge::InitializeDamageTypeModifiers()
{
    DamageTypeModifiers.Empty();
    
    // Configurar modificadores padrão para cada tipo de dano
    FAuracronDamageTypeModifiers PhysicalMods;
    PhysicalMods.BaseMultiplier = 1.0f;
    PhysicalMods.BaseResistance = 0.0f;
    PhysicalMods.ResistanceBuildupRate = 0.08f;
    PhysicalMods.MaxResistance = 0.4f;
    DamageTypeModifiers.Add(EAuracronDamageType::Physical, PhysicalMods);
    
    FAuracronDamageTypeModifiers MagicalMods;
    MagicalMods.BaseMultiplier = 1.1f;
    MagicalMods.BaseResistance = 0.0f;
    MagicalMods.ResistanceBuildupRate = 0.1f;
    MagicalMods.MaxResistance = 0.5f;
    DamageTypeModifiers.Add(EAuracronDamageType::Magical, MagicalMods);
    
    FAuracronDamageTypeModifiers FireMods;
    FireMods.BaseMultiplier = 1.2f;
    FireMods.BaseResistance = 0.0f;
    FireMods.ResistanceBuildupRate = 0.12f;
    FireMods.MaxResistance = 0.6f;
    DamageTypeModifiers.Add(EAuracronDamageType::Fire, FireMods);
    
    FAuracronDamageTypeModifiers IceMods;
    IceMods.BaseMultiplier = 0.9f;
    IceMods.BaseResistance = 0.0f;
    IceMods.ResistanceBuildupRate = 0.15f;
    IceMods.MaxResistance = 0.7f;
    DamageTypeModifiers.Add(EAuracronDamageType::Ice, IceMods);
    
    FAuracronDamageTypeModifiers LightningMods;
    LightningMods.BaseMultiplier = 1.3f;
    LightningMods.BaseResistance = 0.0f;
    LightningMods.ResistanceBuildupRate = 0.06f;
    LightningMods.MaxResistance = 0.3f;
    DamageTypeModifiers.Add(EAuracronDamageType::Lightning, LightningMods);
    
    FAuracronDamageTypeModifiers PoisonMods;
    PoisonMods.BaseMultiplier = 0.8f;
    PoisonMods.BaseResistance = 0.1f;
    PoisonMods.ResistanceBuildupRate = 0.2f;
    PoisonMods.MaxResistance = 0.8f;
    DamageTypeModifiers.Add(EAuracronDamageType::Poison, PoisonMods);
    
    FAuracronDamageTypeModifiers PsychicMods;
    PsychicMods.BaseMultiplier = 1.1f;
    PsychicMods.BaseResistance = 0.0f;
    PsychicMods.ResistanceBuildupRate = 0.05f;
    PsychicMods.MaxResistance = 0.25f;
    DamageTypeModifiers.Add(EAuracronDamageType::Psychic, PsychicMods);
    
    FAuracronDamageTypeModifiers TrueMods;
    TrueMods.BaseMultiplier = 1.0f;
    TrueMods.BaseResistance = 0.0f;
    TrueMods.ResistanceBuildupRate = 0.0f;
    TrueMods.MaxResistance = 0.0f;
    DamageTypeModifiers.Add(EAuracronDamageType::TrueDamage, TrueMods);
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Damage type modifiers initialized for %s"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
}

// === Automatic Layer Transition System Implementation ===

void UAuracronCombatBridge::CheckForAutomaticLayerTransition()
{
    if (!GetOwner() || !GetWorld())
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastTransitionCheck < TransitionCheckInterval)
    {
        return;
    }
    
    LastTransitionCheck = CurrentTime;
    FVector CurrentPosition = GetOwner()->GetActorLocation();
    
    // Detectar mudanças significativas de altura
    float HeightDifference = FMath::Abs(CurrentPosition.Z - PreviousPosition.Z);
    if (HeightDifference > 50.0f) // Mudança mínima para considerar transição
    {
        EAuracronCombatLayer NewLayer = GetLayerFromCurrentPosition();
        if (NewLayer != CurrentCombatLayer && !bIsInLayerTransition)
        {
            ForceLayerTransition(NewLayer, 1.0f);
            UE_LOG(LogAuracronCombat, Log, TEXT("Automatic layer transition triggered: %d -> %d"), 
                   (int32)CurrentCombatLayer, (int32)NewLayer);
        }
    }
    
    PreviousPosition = CurrentPosition;
}

void UAuracronCombatBridge::ForceLayerTransition(EAuracronCombatLayer TargetLayer, float TransitionDuration)
{
    if (TargetLayer == CurrentCombatLayer || bIsInLayerTransition)
    {
        return;
    }
    
    bIsInLayerTransition = true;
    TransitionTargetLayer = TargetLayer;
    CurrentTransitionDuration = TransitionDuration;
    TransitionTimeRemaining = TransitionDuration;
    
    // Configurar timer para transição suave
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            LayerTransitionTimer,
            [this]()
            {
                TransitionTimeRemaining -= 0.1f;
                
                if (TransitionTimeRemaining <= 0.0f)
                {
                    // Completar transição
                    SetCombatLayer(TransitionTargetLayer);
                    bIsInLayerTransition = false;
                    TransitionTimeRemaining = 0.0f;
                    
                    if (GetWorld())
                    {
                        GetWorld()->GetTimerManager().ClearTimer(LayerTransitionTimer);
                    }
                    
                    UE_LOG(LogAuracronCombat, Log, TEXT("Layer transition completed to %d"), (int32)TransitionTargetLayer);
                }
            },
            0.1f,
            true
        );
    }
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Started layer transition from %d to %d (%.2fs)"), 
           (int32)CurrentCombatLayer, (int32)TargetLayer, TransitionDuration);
}

bool UAuracronCombatBridge::IsInLayerTransitionZone() const
{
    if (!GetOwner())
    {
        return false;
    }
    
    float CurrentHeight = GetOwner()->GetActorLocation().Z;
    
    // Verificar se está nas zonas de transição
    bool bNearSkyTransition = FMath::Abs(CurrentHeight - SkyLayerMinHeight) <= TransitionZoneHeight;
    bool bNearUndergroundTransition = FMath::Abs(CurrentHeight - UndergroundLayerMaxHeight) <= TransitionZoneHeight;
    
    return bNearSkyTransition || bNearUndergroundTransition || bIsInLayerTransition;
}

EAuracronCombatLayer UAuracronCombatBridge::GetLayerFromCurrentPosition() const
{
    if (!GetOwner())
    {
        return EAuracronCombatLayer::Surface;
    }
    
    float CurrentHeight = GetOwner()->GetActorLocation().Z;
    
    // Determinar camada baseada na altura
    if (CurrentHeight >= SkyLayerMinHeight)
    {
        return EAuracronCombatLayer::Sky;
    }
    else if (CurrentHeight <= UndergroundLayerMaxHeight)
    {
        return EAuracronCombatLayer::Underground;
    }
    else
    {
        return EAuracronCombatLayer::Surface;
    }
}

// === Advanced AI Targeting System Implementation ===

float UAuracronCombatBridge::CalculateAdvancedTargetPriority(AActor* Target, const FAuracronTargetingContext& Context) const
{
    if (!Target || !GetOwner())
    {
        return 0.0f;
    }
    
    float Priority = 100.0f; // Base priority
    
    // Fator de distância com peso configurável
    float Distance = CalculateLayerAwareDistance(Target);
    float DistanceFactor = FMath::Clamp(1.0f - (Distance / GetMaxRangeForCurrentLayer()), 0.1f, 1.0f);
    Priority *= (DistanceFactor * Context.DistanceWeight);
    
    // Análise de ameaça
    if (Context.ThreatWeight > 0.0f)
    {
        float ThreatLevel = AnalyzeThreatLevel(Target);
        Priority *= (1.0f + (ThreatLevel * Context.ThreatWeight * ThreatAnalysisWeight));
    }
    
    // Valor tático
    if (Context.bConsiderTacticalValue)
    {
        float TacticalValue = CalculateTacticalValue(Target);
        Priority *= (1.0f + (TacticalValue * TacticalValueWeight));
    }
    
    // Fator de saúde com peso configurável
    if (Context.bPrioritizeWeakTargets && Context.HealthWeight > 0.0f)
    {
        float SurvivalScore = CalculateSurvivalScore(Target);
        float HealthFactor = 1.0f - SurvivalScore; // Menor sobrevivência = maior prioridade
        Priority *= (1.0f + (HealthFactor * Context.HealthWeight));
    }
    
    // Fator de camada preferida
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (Context.PreferredLayers.Contains(TargetLayer))
    {
        Priority *= 1.5f;
    }
    else if (TargetLayer == CurrentCombatLayer)
    {
        Priority *= 1.3f; // Bônus menor para mesma camada se não é preferida
    }
    
    // Predição de movimento
    if (Context.bUsePredictiveTargeting)
    {
        FVector PredictedPosition = PredictTargetMovement(Target, 1.0f);
        float PredictedDistance = FVector::Dist(GetOwner()->GetActorLocation(), PredictedPosition);
        if (PredictedDistance <= GetMaxRangeForCurrentLayer())
        {
            Priority *= 1.2f; // Bônus para alvos que permanecerão em alcance
        }
        else
        {
            Priority *= 0.7f; // Penalidade para alvos que sairão de alcance
        }
    }
    
    // Fator de linha de visão
    if (HasLineOfSight3D(Target))
    {
        Priority *= 1.2f;
    }
    
    // Ajuste baseado no nível de agressão
    Priority *= (0.5f + (Context.AggressionLevel * 0.5f));
    
    return Priority;
}

float UAuracronCombatBridge::AnalyzeThreatLevel(AActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }
    
    float ThreatLevel = 0.5f; // Base threat
    
    // Verificar se já temos dados de ameaça para este alvo
    if (ThreatLevels.Contains(Target))
    {
        ThreatLevel = ThreatLevels[Target];
    }
    
    // Analisar componentes de combate do alvo
    if (UAuracronCombatBridge* TargetCombat = Target->FindComponentByClass<UAuracronCombatBridge>())
    {
        // Alvo com sistema de combate é mais ameaçador
        ThreatLevel += 0.3f;
        
        // Verificar se está em combate ativo
        if (TargetCombat->bInCombat)
        {
            ThreatLevel += 0.2f;
        }
    }
    
    // Analisar distância (alvos próximos são mais ameaçadores)
    float Distance = CalculateLayerAwareDistance(Target);
    float MaxRange = GetMaxRangeForCurrentLayer();
    if (Distance < MaxRange * 0.3f)
    {
        ThreatLevel += 0.4f; // Muito próximo
    }
    else if (Distance < MaxRange * 0.6f)
    {
        ThreatLevel += 0.2f; // Moderadamente próximo
    }
    
    // Verificar se o alvo está nos mirando
    if (APawn* TargetPawn = Cast<APawn>(Target))
    {
        FVector ToUs = (GetOwner()->GetActorLocation() - Target->GetActorLocation()).GetSafeNormal();
        FVector TargetForward = Target->GetActorForwardVector();
        float DotProduct = FVector::DotProduct(TargetForward, ToUs);
        
        if (DotProduct > 0.7f) // Está olhando para nós
        {
            ThreatLevel += 0.3f;
        }
    }
    
    return FMath::Clamp(ThreatLevel, 0.0f, 2.0f);
}

float UAuracronCombatBridge::CalculateTacticalValue(AActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }
    
    float TacticalValue = 0.5f; // Base value
    
    // Valor baseado no tipo de alvo
    if (Target->IsA<APawn>())
    {
        TacticalValue += 0.3f; // Pawns são tacticamente valiosos
    }
    
    // Verificar se é um alvo de alta prioridade (ex: healers, support)
    // Isso poderia ser expandido com tags ou componentes específicos
    if (Target->Tags.Contains("HighPriority"))
    {
        TacticalValue += 0.5f;
    }
    else if (Target->Tags.Contains("Support"))
    {
        TacticalValue += 0.4f;
    }
    else if (Target->Tags.Contains("Healer"))
    {
        TacticalValue += 0.6f;
    }
    
    // Valor baseado na posição tática
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (TargetLayer == EAuracronCombatLayer::Sky)
    {
        TacticalValue += 0.2f; // Alvos aéreos podem ter vantagem
    }
    else if (TargetLayer == EAuracronCombatLayer::Underground)
    {
        TacticalValue += 0.1f; // Alvos subterrâneos podem ser mais difíceis de alcançar
    }
    
    // Verificar se o alvo está isolado (mais fácil de eliminar)
    TArray<AActor*> NearbyTargets = FindTargetsInRadius(500.0f, {TargetLayer}, false);
    if (NearbyTargets.Num() <= 2) // Apenas o próprio alvo ou mais um
    {
        TacticalValue += 0.3f; // Alvo isolado é mais valioso
    }
    
    return FMath::Clamp(TacticalValue, 0.0f, 2.0f);
}

FVector UAuracronCombatBridge::PredictTargetMovement(AActor* Target, float PredictionTime) const
{
    if (!Target)
    {
        return FVector::ZeroVector;
    }
    
    FVector CurrentPosition = Target->GetActorLocation();
    
    // Verificar se temos histórico de movimento para este alvo
    if (!MovementHistory.Contains(Target) || MovementHistory[Target].Positions.Num() < 2)
    {
        return CurrentPosition; // Sem dados suficientes, retornar posição atual
    }
    
    const TArray<FVector>& History = MovementHistory[Target].Positions;
    
    // Calcular velocidade média baseada no histórico
    FVector AverageVelocity = FVector::ZeroVector;
    int32 ValidSamples = 0;
    
    for (int32 i = 1; i < History.Num(); ++i)
    {
        FVector Velocity = (History[i] - History[i-1]) / MovementTrackingInterval;
        if (!Velocity.IsNearlyZero())
        {
            AverageVelocity += Velocity;
            ValidSamples++;
        }
    }
    
    if (ValidSamples > 0)
    {
        AverageVelocity /= ValidSamples;
    }
    
    // Predizer posição futura
    FVector PredictedPosition = CurrentPosition + (AverageVelocity * PredictionTime);
    
    return PredictedPosition;
}

AActor* UAuracronCombatBridge::FindBestTargetWithAI(float MaxRange, const FAuracronTargetingContext& Context) const
{
    TArray<EAuracronCombatLayer> SearchLayers = Context.PreferredLayers;
    if (SearchLayers.IsEmpty())
    {
        // Usar todas as camadas se nenhuma preferida foi especificada
        SearchLayers.Add(EAuracronCombatLayer::Surface);
        SearchLayers.Add(EAuracronCombatLayer::Sky);
        SearchLayers.Add(EAuracronCombatLayer::Underground);
    }
    
    TArray<AActor*> PotentialTargets = FindTargetsInRadius(MaxRange, SearchLayers, true);
    
    if (PotentialTargets.IsEmpty())
    {
        return nullptr;
    }
    
    // Ordenar por prioridade avançada
    PotentialTargets.Sort([this, &Context](const AActor& A, const AActor& B) {
        float PriorityA = CalculateAdvancedTargetPriority(const_cast<AActor*>(&A), Context);
        float PriorityB = CalculateAdvancedTargetPriority(const_cast<AActor*>(&B), Context);
        return PriorityA > PriorityB;
    });
    
    AActor* BestTarget = PotentialTargets[0];
    
    UE_LOG(LogAuracronCombat, Log, TEXT("AI Best target found: %s with priority %.2f"), 
           BestTarget ? *BestTarget->GetName() : TEXT("None"),
           CalculateAdvancedTargetPriority(BestTarget, Context));
    
    return BestTarget;
}

float UAuracronCombatBridge::EvaluateAttackEffectiveness(AActor* Target, const FString& AttackType) const
{
    if (!Target)
    {
        return 0.0f;
    }
    
    float Effectiveness = 1.0f; // Base effectiveness
    
    // Fator de distância
    float Distance = CalculateLayerAwareDistance(Target);
    float OptimalRange = GetMaxRangeForCurrentLayer() * 0.6f; // 60% do alcance máximo é ótimo
    
    if (Distance <= OptimalRange)
    {
        Effectiveness *= 1.2f; // Bônus para distância ótima
    }
    else
    {
        float RangePenalty = (Distance - OptimalRange) / (GetMaxRangeForCurrentLayer() - OptimalRange);
        Effectiveness *= (1.0f - (RangePenalty * 0.3f)); // Até 30% de penalidade
    }
    
    // Fator de camada
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    float LayerModifier = CalculateLayerDamageModifier(Target);
    Effectiveness *= LayerModifier;
    
    // Fator de linha de visão
    if (!HasLineOfSight3D(Target))
    {
        Effectiveness *= 0.5f; // Penalidade severa sem linha de visão
    }
    
    // Fator específico do tipo de ataque
    if (!AttackType.IsEmpty())
    {
        if (AttackType == "Ranged" && TargetLayer == EAuracronCombatLayer::Sky)
        {
            Effectiveness *= 1.3f; // Ataques ranged são melhores contra alvos aéreos
        }
        else if (AttackType == "Melee" && TargetLayer == CurrentCombatLayer)
        {
            Effectiveness *= 1.4f; // Ataques melee são melhores na mesma camada
        }
        else if (AttackType == "AOE")
        {
            // Verificar quantos alvos estão próximos para AOE
            TArray<AActor*> NearbyTargets = FindTargetsInRadius(300.0f, {TargetLayer}, false);
            if (NearbyTargets.Num() > 2)
            {
                Effectiveness *= (1.0f + (NearbyTargets.Num() * 0.2f)); // Bônus por múltiplos alvos
            }
        }
    }
    
    // Verificar histórico de sucesso contra este alvo
    if (TargetingSuccessRates.Contains(Target))
    {
        float SuccessRate = TargetingSuccessRates[Target];
        Effectiveness *= (0.7f + (SuccessRate * 0.6f)); // Ajustar baseado no histórico
    }
    
    return FMath::Clamp(Effectiveness, 0.1f, 3.0f);
}

void UAuracronCombatBridge::UpdateTargetingLearning(AActor* Target, bool bSuccessfulAttack, float DamageDealt)
{
    if (!Target)
    {
        return;
    }
    
    // Atualizar taxa de sucesso
    float CurrentSuccessRate = TargetingSuccessRates.Contains(Target) ? TargetingSuccessRates[Target] : 0.5f;
    
    // Usar média móvel para suavizar as mudanças
    float LearningRate = 0.1f;
    float NewSuccessValue = bSuccessfulAttack ? 1.0f : 0.0f;
    CurrentSuccessRate = (CurrentSuccessRate * (1.0f - LearningRate)) + (NewSuccessValue * LearningRate);
    
    TargetingSuccessRates.Add(Target, CurrentSuccessRate);
    
    // Atualizar nível de ameaça baseado no dano causado/recebido
    float CurrentThreat = ThreatLevels.Contains(Target) ? ThreatLevels[Target] : 0.5f;
    
    if (bSuccessfulAttack && DamageDealt > 0.0f)
    {
        // Se causamos dano, o alvo pode se tornar mais agressivo (mais ameaçador)
        CurrentThreat += 0.1f;
    }
    else if (!bSuccessfulAttack)
    {
        // Se falhamos, o alvo pode ser menos ameaçador do que pensávamos
        CurrentThreat -= 0.05f;
    }
    
    ThreatLevels.Add(Target, FMath::Clamp(CurrentThreat, 0.0f, 2.0f));
    
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Updated targeting learning for %s: Success Rate %.2f, Threat Level %.2f"),
           *Target->GetName(), CurrentSuccessRate, CurrentThreat);
}

float UAuracronCombatBridge::CalculateSurvivalScore(AActor* Target) const
{
    if (!Target)
    {
        return 0.0f;
    }
    
    float SurvivalScore = 0.5f; // Base survival score
    
    // Verificar sistema de saúde do alvo
    if (AAuracronCharacter* AuracronChar = Cast<AAuracronCharacter>(Target))
    {
        // Assumindo que AuracronCharacter tem sistema de saúde
        // SurvivalScore = AuracronChar->GetHealthPercentage();
        
        // Por enquanto, usar uma estimativa baseada em outros fatores
        if (AuracronChar->IsAlive())
        {
            SurvivalScore = 0.8f; // Assumir que está com boa saúde se está vivo
        }
        else
        {
            SurvivalScore = 0.0f;
        }
    }
    
    // Fator de distância (alvos mais distantes têm maior chance de sobreviver)
    float Distance = CalculateLayerAwareDistance(Target);
    float MaxRange = GetMaxRangeForCurrentLayer();
    float DistanceFactor = Distance / MaxRange;
    SurvivalScore += (DistanceFactor * 0.2f);
    
    // Fator de camada (algumas camadas oferecem mais proteção)
    EAuracronCombatLayer TargetLayer = GetActorCombatLayer(Target);
    if (TargetLayer == EAuracronCombatLayer::Underground)
    {
        SurvivalScore += 0.1f; // Underground oferece mais proteção
    }
    else if (TargetLayer == EAuracronCombatLayer::Sky)
    {
        SurvivalScore -= 0.1f; // Sky é mais exposto
    }
    
    // Fator de linha de visão (sem linha de visão = maior sobrevivência)
    if (!HasLineOfSight3D(Target))
    {
        SurvivalScore += 0.2f;
    }
    
    return FMath::Clamp(SurvivalScore, 0.0f, 1.0f);
}

void UAuracronCombatBridge::UpdateMovementTracking()
{
    if (!GetOwner())
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Verificar se é hora de atualizar o rastreamento
    if (CurrentTime - LastMovementUpdate < MovementTrackingInterval)
    {
        return;
    }
    
    LastMovementUpdate = CurrentTime;
    
    // Encontrar todos os alvos potenciais para rastrear
    TArray<EAuracronCombatLayer> AllLayers = {
        EAuracronCombatLayer::Surface,
        EAuracronCombatLayer::Sky,
        EAuracronCombatLayer::Underground
    };
    
    TArray<AActor*> AllTargets = FindTargetsInRadius(GetMaxRangeForCurrentLayer() * 1.5f, AllLayers, false);
    
    // Atualizar histórico de movimento para cada alvo
    for (AActor* Target : AllTargets)
    {
        if (!Target)
        {
            continue;
        }
        
        FVector CurrentPosition = Target->GetActorLocation();
        
        // Inicializar estrutura se não existir
        if (!MovementHistory.Contains(Target))
        {
            MovementHistory.Add(Target, FAuracronMovementHistory());
        }
        
        FAuracronMovementHistory& HistoryData = MovementHistory[Target];
        float CurrentTimeStamp = GetWorld()->GetTimeSeconds();
        
        // Adicionar posição e timestamp atuais
        HistoryData.Positions.Add(CurrentPosition);
        HistoryData.Timestamps.Add(CurrentTimeStamp);
        
        // Limitar o tamanho do histórico
        if (HistoryData.Positions.Num() > MaxMovementHistorySize)
        {
            HistoryData.Positions.RemoveAt(0); // Remove a posição mais antiga
            HistoryData.Timestamps.RemoveAt(0); // Remove o timestamp mais antigo
        }
    }
    
    // Limpar histórico de alvos que não estão mais em alcance
    TArray<AActor*> TargetsToRemove;
    for (auto& Pair : MovementHistory)
    {
        AActor* TrackedTarget = Pair.Key.Get();
        if (!TrackedTarget || !AllTargets.Contains(TrackedTarget))
        {
            TargetsToRemove.Add(TrackedTarget);
        }
    }
    
    for (AActor* TargetToRemove : TargetsToRemove)
    {
        MovementHistory.Remove(TargetToRemove);
        // Também limpar outros dados relacionados
        ThreatLevels.Remove(TargetToRemove);
        TargetingSuccessRates.Remove(TargetToRemove);
    }
    
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Movement tracking updated. Tracking %d targets."), MovementHistory.Num());
}

// === Advanced Combo System Implementation ===

FString UAuracronCombatBridge::DetectSpecialCombo() const
{
    if (CurrentAttackSequence.Num() < MIN_COMBO_LENGTH)
    {
        return TEXT("");
    }
    
    // Converter sequência para string para análise
    FString SequenceString;
    for (const FString& Attack : CurrentAttackSequence)
    {
        SequenceString += Attack + TEXT("-");
    }
    
    // Detectar padrões de combo específicos
    
    // Combo "Elemental Fury" - 3 tipos de dano elemental diferentes
    if (CurrentAttackSequence.Num() >= 3)
    {
        TSet<FString> ElementalTypes = {TEXT("Fire"), TEXT("Ice"), TEXT("Lightning"), TEXT("Earth")};
        TSet<FString> UsedElements;
        
        for (int32 i = CurrentAttackSequence.Num() - 3; i < CurrentAttackSequence.Num(); ++i)
        {
            if (ElementalTypes.Contains(CurrentAttackSequence[i]))
            {
                UsedElements.Add(CurrentAttackSequence[i]);
            }
        }
        
        if (UsedElements.Num() >= 3)
        {
            return TEXT("Elemental Fury");
        }
    }
    
    // Combo "Perfect Strike" - 5 ataques do mesmo tipo
    if (CurrentAttackSequence.Num() >= 5)
    {
        FString LastType = CurrentAttackSequence.Last();
        bool bSameType = true;
        
        for (int32 i = CurrentAttackSequence.Num() - 5; i < CurrentAttackSequence.Num(); ++i)
        {
            if (CurrentAttackSequence[i] != LastType)
            {
                bSameType = false;
                break;
            }
        }
        
        if (bSameType && !LastType.IsEmpty())
        {
            return TEXT("Perfect Strike");
        }
    }
    
    // Combo "Chain Lightning" - alternância entre Lightning e qualquer outro
    if (CurrentAttackSequence.Num() >= 4)
    {
        bool bIsChainLightning = true;
        for (int32 i = CurrentAttackSequence.Num() - 4; i < CurrentAttackSequence.Num(); i += 2)
        {
            if (CurrentAttackSequence[i] != TEXT("Lightning"))
            {
                bIsChainLightning = false;
                break;
            }
        }
        
        if (bIsChainLightning)
        {
            return TEXT("Chain Lightning");
        }
    }
    
    // Combo "Berserker Rage" - 6+ ataques consecutivos em menos de 2 segundos cada
    if (CurrentAttackSequence.Num() >= 6 && ConsecutiveAttacks >= 6)
    {
        return TEXT("Berserker Rage");
    }
    
    // Combo "Tactical Strike" - Physical -> Magic -> Physical
    if (CurrentAttackSequence.Num() >= 3)
    {
        TArray<FString> LastThree;
        for (int32 i = CurrentAttackSequence.Num() - 3; i < CurrentAttackSequence.Num(); ++i)
        {
            LastThree.Add(CurrentAttackSequence[i]);
        }
        
        if (LastThree[0] == TEXT("Physical") && 
            LastThree[1] == TEXT("Magic") && 
            LastThree[2] == TEXT("Physical"))
        {
            return TEXT("Tactical Strike");
        }
    }
    
    return TEXT("");
}

float UAuracronCombatBridge::CalculateSpecialComboMultiplier(const FString& ComboName) const
{
    if (ComboName.IsEmpty())
    {
        return 1.0f;
    }
    
    float Multiplier = SPECIAL_COMBO_BASE_MULTIPLIER;
    
    // Multiplicadores específicos por tipo de combo
    if (ComboName == TEXT("Elemental Fury"))
    {
        Multiplier = 2.0f + (ConsecutiveAttacks * 0.1f); // Cresce com ataques consecutivos
    }
    else if (ComboName == TEXT("Perfect Strike"))
    {
        Multiplier = 2.5f + (ConsecutiveAttacks * 0.15f); // Maior multiplicador base
    }
    else if (ComboName == TEXT("Chain Lightning"))
    {
        Multiplier = 1.8f + (CurrentAttackSequence.Num() * 0.2f); // Cresce com tamanho da sequência
    }
    else if (ComboName == TEXT("Berserker Rage"))
    {
        Multiplier = 3.0f + (ConsecutiveAttacks * 0.05f); // Alto multiplicador base, crescimento lento
    }
    else if (ComboName == TEXT("Tactical Strike"))
    {
        Multiplier = 1.7f + (GetCurrentCombatLayer() == EAuracronCombatLayer::Surface ? 0.3f : 0.0f);
    }
    
    // Aplicar bônus baseado no tempo de combo ativo
    if (SpecialComboTimeRemaining > 0.0f)
    {
        float TimeBonus = (SpecialComboTimeRemaining / SPECIAL_COMBO_DURATION) * 0.5f;
        Multiplier += TimeBonus;
    }
    
    // Aplicar limite máximo
    Multiplier = FMath::Min(Multiplier, MAX_SPECIAL_COMBO_MULTIPLIER);
    
    return Multiplier;
}

void UAuracronCombatBridge::ResetComboSystem()
{
    ConsecutiveAttacks = 0;
    CurrentComboMultiplier = 1.0f;
    CurrentAttackSequence.Empty();
    ActiveSpecialCombo = TEXT("");
    SpecialComboMultiplier = 1.0f;
    SpecialComboTimeRemaining = 0.0f;
    LastAttackTime = 0.0f;
    
    UE_LOG(LogAuracronCombat, Log, TEXT("%s combo system reset"), 
           GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
}

bool UAuracronCombatBridge::IsComboActive() const
{
    if (!GetWorld())
    {
        return false;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    bool bBasicComboActive = (CurrentTime - LastAttackTime) <= COMBO_RESET_TIME && ConsecutiveAttacks > 0;
    bool bSpecialComboActive = !ActiveSpecialCombo.IsEmpty() && SpecialComboTimeRemaining > 0.0f;
    
    return bBasicComboActive || bSpecialComboActive;
}

FString UAuracronCombatBridge::GetCurrentComboInfo() const
{
    if (!IsComboActive())
    {
        return TEXT("No active combo");
    }
    
    FString ComboInfo;
    
    // Informações do combo básico
    if (ConsecutiveAttacks > 0)
    {
        ComboInfo += FString::Printf(TEXT("Basic Combo: %dx (%.2f multiplier)"), 
                                   ConsecutiveAttacks, CurrentComboMultiplier);
    }
    
    // Informações do combo especial
    if (!ActiveSpecialCombo.IsEmpty())
    {
        if (!ComboInfo.IsEmpty())
        {
            ComboInfo += TEXT(" | ");
        }
        
        ComboInfo += FString::Printf(TEXT("Special: %s (%.2fx, %.1fs remaining)"), 
                                   *ActiveSpecialCombo, SpecialComboMultiplier, SpecialComboTimeRemaining);
    }
    
    // Adicionar sequência atual
    if (CurrentAttackSequence.Num() > 0)
    {
        ComboInfo += TEXT(" | Sequence: ");
        for (int32 i = 0; i < CurrentAttackSequence.Num(); ++i)
        {
            ComboInfo += CurrentAttackSequence[i];
            if (i < CurrentAttackSequence.Num() - 1)
            {
                ComboInfo += TEXT("-");
            }
        }
    }
    
    return ComboInfo;
}

void UAuracronCombatBridge::UpdateSpecialComboSystem(float DeltaTime)
{
    // Atualizar tempo restante do combo especial
    if (SpecialComboTimeRemaining > 0.0f)
    {
        SpecialComboTimeRemaining -= DeltaTime;
        
        // Verificar se o combo especial expirou
        if (SpecialComboTimeRemaining <= 0.0f)
        {
            UE_LOG(LogAuracronCombat, Log, TEXT("%s special combo '%s' expired"), 
                   GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"),
                   *ActiveSpecialCombo);
            
            // Resetar combo especial
            ActiveSpecialCombo = TEXT("");
            SpecialComboMultiplier = 1.0f;
            SpecialComboTimeRemaining = 0.0f;
        }
        else
        {
            // Recalcular multiplicador baseado no tempo restante
            SpecialComboMultiplier = CalculateSpecialComboMultiplier(ActiveSpecialCombo);
        }
    }
    
    // Verificar se o combo básico deve ser resetado
    if (GetWorld() && (GetWorld()->GetTimeSeconds() - LastAttackTime) > COMBO_RESET_TIME)
    {
        if (ConsecutiveAttacks > 0)
        {
            UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("%s basic combo reset due to timeout"), 
                   GetOwner() ? *GetOwner()->GetName() : TEXT("Unknown"));
            
            ConsecutiveAttacks = 0;
            CurrentComboMultiplier = 1.0f;
            
            // Limpar sequência de ataques se não há combo especial ativo
            if (ActiveSpecialCombo.IsEmpty())
            {
                CurrentAttackSequence.Empty();
            }
        }
    }
}