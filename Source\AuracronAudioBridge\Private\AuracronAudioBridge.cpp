// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Ãudio MetaSounds Bridge Implementation

#include "AuracronAudioBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundCue.h"
#include "GameFramework/GameUserSettings.h"
// MetasoundSource functionality handled by forward declarations
#include "MetasoundGenerator.h"
#include "MetasoundParameterTransmitter.h"
#include "AudioMixerBlueprintLibrary.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "EngineUtils.h"
#include "Async/Async.h"
#include "Kismet/GameplayStatics.h"
#include "AudioMixer.h"
UAuracronAudioBridge::UAuracronAudioBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para Ã¡udio responsivo
    
    // Configurar replicaÃ§Ã£o
    SetIsReplicatedByDefault(true);
    
    // ConfiguraÃ§Ãµes padrÃ£o
    AudioConfiguration.MasterVolume = 1.0f;
    AudioConfiguration.MusicVolume = 0.8f;
    AudioConfiguration.SFXVolume = 1.0f;
    AudioConfiguration.VoiceVolume = 1.0f;
    AudioConfiguration.AmbientVolume = 0.6f;
    AudioConfiguration.UIVolume = 0.8f;
    AudioConfiguration.bUse3DAudio = true;
    AudioConfiguration.bUseDynamicReverb = true;
    AudioConfiguration.bUseAudioOcclusion = true;
    AudioConfiguration.bUseDynamicRangeCompression = false;
    AudioConfiguration.AudioQuality = 1.0f;
    AudioConfiguration.MaxAudioDistance = 5000.0f;
    AudioConfiguration.bUseBinauralAudio = false;
    AudioConfiguration.bUseHRTF = false;
    AudioConfiguration.CurrentAudioLayer = EAuracronAudioLayer::Surface;
    AudioConfiguration.bUseSmoothLayerTransitions = true;
    AudioConfiguration.LayerTransitionTime = 1.0f;
    
    DynamicMusicConfiguration.bUseAdaptiveTransitions = true;
    DynamicMusicConfiguration.bCombatBasedIntensity = true;
    DynamicMusicConfiguration.MusicFadeTime = 2.0f;
    DynamicMusicConfiguration.DynamicMusicVolume = 0.8f;
    
    SFXConfiguration.bUseRandomVariations = true;
    SFXConfiguration.MaxSimultaneousSounds = 32;
    SFXConfiguration.bUseAudioComponentPooling = true;
    SFXConfiguration.AudioComponentPoolSize = 20;
    
    CurrentMusicType = TEXT("None");
    CurrentMusicIntensity = 0.5f;
}

void UAuracronAudioBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Ãudio MetaSounds"));

    // Inicializar sistema
    bSystemInitialized = InitializeAudioSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timer para transiÃ§Ãµes
        GetWorld()->GetTimerManager().SetTimer(
            TransitionTimer,
            [this]()
            {
                ProcessAudioTransitions(0.1f);
                Update3DAudio(0.1f);
            },
            0.1f,
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Ãudio MetaSounds inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Ãudio MetaSounds"));
    }
}

void UAuracronAudioBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Parar todos os sons
    PauseAllSounds();
    
    // Limpar componentes ativos
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component))
        {
            Component->Stop();
            Component->DestroyComponent();
        }
    }
    ActiveAudioComponents.Empty();
    
    // Limpar pool
    for (UAudioComponent* Component : AudioComponentPool)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    AudioComponentPool.Empty();

    // Limpar timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(TransitionTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronAudioBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronAudioBridge, AudioConfiguration);
    DOREPLIFETIME(UAuracronAudioBridge, CurrentMusicType);
    DOREPLIFETIME(UAuracronAudioBridge, CurrentMusicIntensity);
}

void UAuracronAudioBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar transiÃ§Ãµes de Ã¡udio
    ProcessAudioTransitions(DeltaTime);
    
    // Atualizar Ã¡udio 3D
    Update3DAudio(DeltaTime);
    
    // Limpar componentes inativos
    for (int32 i = ActiveAudioComponents.Num() - 1; i >= 0; i--)
    {
        if (!IsValid(ActiveAudioComponents[i]) || !ActiveAudioComponents[i]->IsPlaying())
        {
            ActiveAudioComponents.RemoveAt(i);
        }
    }
}

// === Core Audio Management ===

bool UAuracronAudioBridge::PlaySound3D(USoundBase* Sound, const FVector& Location, float Volume, float Pitch)
{
    if (!bSystemInitialized || !Sound)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado ou som invÃ¡lido"));
        return false;
    }

    // Verificar limite de sons simultÃ¢neos
    if (ActiveAudioComponents.Num() >= SFXConfiguration.MaxSimultaneousSounds)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Limite de sons simultÃ¢neos atingido"));
        return false;
    }

    // Obter componente de Ã¡udio do pool ou criar novo usando UE5.6 otimizado
    UAudioComponent* AudioComponent = nullptr;
    
    if (SFXConfiguration.bUseAudioComponentPooling && AudioComponentPool.Num() > 0)
    {
        // Reutilizar componente do pool
        AudioComponent = AudioComponentPool.Pop();
        
        // Reconfigurar o componente reutilizado
        if (IsValid(AudioComponent))
        {
            AudioComponent->Stop();
            AudioComponent->SetSound(Sound);
            AudioComponent->SetWorldLocation(Location);
            AudioComponent->SetVolumeMultiplier(Volume);
            AudioComponent->SetPitchMultiplier(Pitch);
            AudioComponent->Play();
            
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Componente de Ã¡udio reutilizado do pool"));
        }
        else
        {
            // Componente invÃ¡lido no pool, remover e criar novo
            AudioComponent = nullptr;
        }
    }
    
    if (!AudioComponent)
    {
        // Criar novo componente usando UE5.6 SpawnSoundAtLocation
        AudioComponent = UGameplayStatics::SpawnSoundAtLocation(
            GetWorld(),
            Sound,
            Location,
            FRotator::ZeroRotator,
            Volume,
            Pitch
        );
        
        if (AudioComponent)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Novo componente de Ã¡udio criado"));
        }
    }

    if (AudioComponent)
    {
        // Configurar Ã¡udio 3D
        if (AudioConfiguration.bUse3DAudio)
        {
            AudioComponent->bAllowSpatialization = true;
            AudioComponent->AttenuationSettings = nullptr; // Usar configuraÃ§Ã£o padrÃ£o
        }

        // Aplicar volume baseado na configuraÃ§Ã£o
        float FinalVolume = Volume * AudioConfiguration.SFXVolume * AudioConfiguration.MasterVolume;
        AudioComponent->SetVolumeMultiplier(FinalVolume);
        AudioComponent->SetPitchMultiplier(Pitch);

        // Configurar delegate para retorno automÃ¡tico ao pool
        if (SFXConfiguration.bUseAudioComponentPooling)
        {
            // Use modern UE5.6 delegate binding for audio finished callback
            // Use modern UE5.6 callback without parameters
            AudioComponent->OnAudioFinished.AddDynamic(this, &UAuracronAudioBridge::OnAudioFinishedModern);
        }

        // Adicionar Ã  lista de componentes ativos
        ActiveAudioComponents.Add(AudioComponent);

        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Som 3D reproduzido em %s"), *Location.ToString());

        return true;
    }

    return false;
}

// === Champion Audio ===

bool UAuracronAudioBridge::PlayChampionVoice(const FString& ChampionID, const FString& VoiceLineType)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado"));
        return false;
    }

    if (ChampionID.IsEmpty() || VoiceLineType.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ChampionID ou VoiceLineType invÃ¡lido"));
        return false;
    }

    // Construir chave para busca no mapa
    FString VoiceKey = FString::Printf(TEXT("%s_%s"), *ChampionID, *VoiceLineType);
    
    // Buscar no mapa de sons de UI (usado para vozes)
    if (SFXConfiguration.UISounds.Contains(VoiceKey))
    {
        TSoftObjectPtr<USoundCue> VoiceSound = SFXConfiguration.UISounds[VoiceKey];
        
        if (VoiceSound.IsValid())
        {
            UAudioComponent* VoiceComponent = UGameplayStatics::SpawnSound2D(
                GetWorld(),
                VoiceSound.Get(),
                AudioConfiguration.VoiceVolume * AudioConfiguration.MasterVolume
            );

            if (VoiceComponent)
            {
                VoiceComponent->bIsUISound = true;
                VoiceComponent->bAutoDestroy = true;
                
                // Aplicar configuraÃ§Ãµes especÃ­ficas de voz
                VoiceComponent->SetPitchMultiplier(FMath::RandRange(0.95f, 1.05f)); // Pequena variaÃ§Ã£o
                
                ActiveAudioComponents.Add(VoiceComponent);
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Voz de campeÃ£o reproduzida: %s - %s"), *ChampionID, *VoiceLineType);
                return true;
            }
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Voz de campeÃ£o nÃ£o encontrada: %s - %s"), *ChampionID, *VoiceLineType);
    return false;
}

bool UAuracronAudioBridge::PlayAbilitySound(const FString& ChampionID, const FString& AbilitySlot, const FVector& Location)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (ChampionID.IsEmpty() || AbilitySlot.IsEmpty())
    {
        return false;
    }

    // Construir chave para busca
    FString AbilityKey = FString::Printf(TEXT("%s_%s"), *ChampionID, *AbilitySlot);
    
    if (SFXConfiguration.ChampionAbilitySounds.Contains(AbilityKey))
    {
        // Get the MetaSound from configuration and cast to USoundBase for modern UE5.6 API
        USoundBase* AbilitySound = Cast<USoundBase>(SFXConfiguration.ChampionAbilitySounds[AbilityKey].Get());

        if (AbilitySound)
        {
            TMap<FString, float> Parameters;
            Parameters.Add(TEXT("ChampionID"), FCString::Atof(*ChampionID.Right(1))); // Assumindo ID numÃ©rico
            Parameters.Add(TEXT("AbilitySlot"), AbilitySlot == TEXT("Q") ? 0.0f : AbilitySlot == TEXT("W") ? 1.0f : AbilitySlot == TEXT("E") ? 2.0f : 3.0f);
            Parameters.Add(TEXT("Intensity"), CurrentMusicIntensity);

            bool bSuccess = PlayMetaSound(AbilitySound, Location, Parameters);
            
            if (bSuccess)
            {
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Som de habilidade reproduzido: %s - %s"), *ChampionID, *AbilitySlot);
            }
            
            return bSuccess;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Som de habilidade nÃ£o encontrado: %s - %s"), *ChampionID, *AbilitySlot);
    return false;
}

bool UAuracronAudioBridge::PlayMovementSound(const FString& MovementType, const FVector& Location)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (MovementType.IsEmpty())
    {
        return false;
    }

    // Usar variaÃ§Ãµes aleatÃ³rias se habilitado
    if (SFXConfiguration.bUseRandomVariations && SFXConfiguration.MovementSounds.Num() > 0)
    {
        int32 RandomIndex = FMath::RandRange(0, SFXConfiguration.MovementSounds.Num() - 1);
        TSoftObjectPtr<USoundCue> MovementSound = SFXConfiguration.MovementSounds[RandomIndex];
        
        if (MovementSound.IsValid())
        {
            float Volume = AudioConfiguration.SFXVolume * AudioConfiguration.MasterVolume;
            float Pitch = FMath::RandRange(0.9f, 1.1f); // VariaÃ§Ã£o de pitch
            
            bool bSuccess = PlaySound3D(MovementSound.Get(), Location, Volume, Pitch);
            
            if (bSuccess)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Som de movimento reproduzido: %s"), *MovementType);
            }
            
            return bSuccess;
        }
    }

    return false;
}

// === Audio Effects ===

bool UAuracronAudioBridge::ApplyReverbEffect(const FString& ReverbType, float Intensity)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (ReverbType.IsEmpty() || Intensity < 0.0f)
    {
        return false;
    }

    // Aplicar reverb usando UE5.6 ActivateReverbEffect API
    if (GetWorld())
    {
        // Criar configuraÃ§Ãµes de reverb baseadas no tipo usando UReverbEffect
        UReverbEffect* ReverbEffect = NewObject<UReverbEffect>();
        if (!ReverbEffect)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao criar UReverbEffect"));
            return false;
        }

        float ReverbGain = FMath::Clamp(Intensity, 0.0f, 1.0f);
        float ReverbTime = 1.0f;
        float ReverbDamping = 0.5f;
        float Density = 1.0f;
        float Diffusion = 1.0f;
        
        // Configurar parÃ¢metros especÃ­ficos por tipo de ambiente
        if (ReverbType == TEXT("Cave"))
        {
            ReverbEffect->Density = 1.0f;
            ReverbEffect->Diffusion = 1.0f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.89f;
            ReverbEffect->DecayTime = 2.91f;
            ReverbEffect->DecayHFRatio = 1.3f;
            ReverbEffect->ReflectionsGain = 0.5f;
            ReverbEffect->ReflectionsDelay = 0.015f;
            ReverbEffect->LateGain = 0.7f;
            ReverbEffect->LateDelay = 0.022f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6 - using modern reverb settings
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else if (ReverbType == TEXT("Underwater"))
        {
            ReverbEffect->Density = 0.36f;
            ReverbEffect->Diffusion = 0.7f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.1f;
            ReverbEffect->DecayTime = 1.49f;
            ReverbEffect->DecayHFRatio = 0.1f;
            ReverbEffect->ReflectionsGain = 0.6f;
            ReverbEffect->ReflectionsDelay = 0.007f;
            ReverbEffect->LateGain = 1.18f;
            ReverbEffect->LateDelay = 0.011f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else if (ReverbType == TEXT("Forest"))
        {
            ReverbEffect->Density = 1.0f;
            ReverbEffect->Diffusion = 0.78f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.3f;
            ReverbEffect->DecayTime = 1.49f;
            ReverbEffect->DecayHFRatio = 0.54f;
            ReverbEffect->ReflectionsGain = 0.05f;
            ReverbEffect->ReflectionsDelay = 0.162f;
            ReverbEffect->LateGain = 0.89f;
            ReverbEffect->LateDelay = 0.088f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else if (ReverbType == TEXT("Sky"))
        {
            ReverbEffect->Density = 1.0f;
            ReverbEffect->Diffusion = 0.96f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.74f;
            ReverbEffect->DecayTime = 1.49f;
            ReverbEffect->DecayHFRatio = 0.87f;
            ReverbEffect->ReflectionsGain = 0.25f;
            ReverbEffect->ReflectionsDelay = 0.029f;
            ReverbEffect->LateGain = 0.89f;
            ReverbEffect->LateDelay = 0.02f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else if (ReverbType == TEXT("Mountain"))
        {
            ReverbEffect->Density = 1.0f;
            ReverbEffect->Diffusion = 0.27f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.56f;
            ReverbEffect->DecayTime = 1.49f;
            ReverbEffect->DecayHFRatio = 0.21f;
            ReverbEffect->ReflectionsGain = 0.04f;
            ReverbEffect->ReflectionsDelay = 0.3f;
            ReverbEffect->LateGain = 0.26f;
            ReverbEffect->LateDelay = 0.1f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else if (ReverbType == TEXT("Dungeon"))
        {
            ReverbEffect->Density = 0.75f;
            ReverbEffect->Diffusion = 1.0f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.53f;
            ReverbEffect->DecayTime = 3.57f;
            ReverbEffect->DecayHFRatio = 0.5f;
            ReverbEffect->ReflectionsGain = 0.0f;
            ReverbEffect->ReflectionsDelay = 0.123f;
            ReverbEffect->LateGain = 1.26f;
            ReverbEffect->LateDelay = 0.017f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }
        else
        {
            // ConfiguraÃ§Ã£o padrÃ£o para tipos desconhecidos
            ReverbEffect->Density = 1.0f;
            ReverbEffect->Diffusion = 1.0f;
            ReverbEffect->Gain = ReverbGain * 0.32f;
            ReverbEffect->GainHF = 0.89f;
            ReverbEffect->DecayTime = 1.49f;
            ReverbEffect->DecayHFRatio = 0.83f;
            ReverbEffect->ReflectionsGain = 0.05f;
            ReverbEffect->ReflectionsDelay = 0.007f;
            ReverbEffect->LateGain = 1.26f;
            ReverbEffect->LateDelay = 0.011f;
            ReverbEffect->AirAbsorptionGainHF = 0.99f;
            // RoomRolloffFactor is deprecated in UE5.6
            // ReverbEffect->RoomRolloffFactor = 0.0f; // Deprecated
        }

        // Ativar o efeito de reverb usando a API do UE5.6
        FName TagName = FName(*FString::Printf(TEXT("AuracronReverb_%s"), *ReverbType));
        float Priority = 1.0f;
        float Volume = ReverbGain;
        float FadeTime = 1.0f;

        UGameplayStatics::ActivateReverbEffect(
            GetWorld(),
            ReverbEffect,
            TagName,
            Priority,
            Volume,
            FadeTime
        );

        // Armazenar referÃªncia do efeito ativo para controle posterior
        CurrentReverbEffect = ReverbEffect;
        CurrentReverbType = ReverbType;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeito de reverb UE5.6 aplicado: %s (Intensidade: %f)"), *ReverbType, Intensity);
        return true;
    }

    return false;
}

bool UAuracronAudioBridge::ApplyDelayEffect(float DelayTime, float Feedback)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de Ã¡udio nÃ£o inicializado"));
        return false;
    }

    // Validar parÃ¢metros de entrada
    if (DelayTime <= 0.0f || Feedback < 0.0f || Feedback > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ParÃ¢metros de delay invÃ¡lidos - DelayTime: %f, Feedback: %f"), DelayTime, Feedback);
        return false;
    }

    // ImplementaÃ§Ã£o usando MetaSound para UE5.6
    // Validar e clampar parÃ¢metros
    float ClampedDelayTime = FMath::Clamp(DelayTime, 0.001f, 2000.0f); // MÃ­nimo de 1ms
    float ClampedFeedback = FMath::Clamp(Feedback, 0.0f, 0.95f); // MÃ¡ximo de 95% para evitar feedback infinito
    
    // Log da configuraÃ§Ã£o do delay
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configurando delay - Tempo: %f ms, Feedback: %f"), ClampedDelayTime, ClampedFeedback);
    
    // Armazenar configuraÃ§Ãµes
    DelaySettings.DelayTime = ClampedDelayTime;
    DelaySettings.Feedback = ClampedFeedback;
    DelaySettings.WetLevel = 0.3f; // 30% wet
    DelaySettings.DryLevel = 0.7f; // 70% dry

    // Aplicar delay aos componentes ativos usando MetaSound
    int32 ComponentsProcessed = 0;
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && !Component->bIsUISound)
        {
            // Configurar parÃ¢metros do MetaSound Delay node
            // Estes parÃ¢metros correspondem aos inputs do MetaSound Delay node
            Component->SetFloatParameter(FName("DelayTime"), ClampedDelayTime / 1000.0f); // Converter ms para segundos
            Component->SetFloatParameter(FName("Feedback"), ClampedFeedback);
            Component->SetFloatParameter(FName("WetLevel"), DelaySettings.WetLevel);
            Component->SetFloatParameter(FName("DryLevel"), DelaySettings.DryLevel);
            
            // ParÃ¢metros adicionais para controle fino
            Component->SetFloatParameter(FName("DelayWetLevel"), DelaySettings.WetLevel);
            Component->SetFloatParameter(FName("DelayDryLevel"), DelaySettings.DryLevel);
            
            ComponentsProcessed++;
        }
    }

    // Atualizar estado atual
    CurrentDelayTime = ClampedDelayTime;
    CurrentDelayFeedback = ClampedFeedback;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeito de delay MetaSound aplicado a %d componentes - Tempo: %f ms, Feedback: %f, Wet: %f, Dry: %f"),
           ComponentsProcessed, ClampedDelayTime, ClampedFeedback, DelaySettings.WetLevel, DelaySettings.DryLevel);
    
    return ComponentsProcessed > 0;
}

bool UAuracronAudioBridge::RemoveDelayEffect()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de Ã¡udio nÃ£o inicializado"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo efeito de delay"));
    
    // Resetar configuraÃ§Ãµes de delay
    DelaySettings.DelayTime = 0.0f;
    DelaySettings.Feedback = 0.0f;
    DelaySettings.WetLevel = 0.0f;
    DelaySettings.DryLevel = 1.0f;

    // Remover delay dos componentes ativos
    int32 ComponentsProcessed = 0;
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && !Component->bIsUISound)
        {
            // Resetar parÃ¢metros do MetaSound Delay node para valores padrÃ£o
            Component->SetFloatParameter(FName("DelayTime"), 0.0f);
            Component->SetFloatParameter(FName("Feedback"), 0.0f);
            Component->SetFloatParameter(FName("WetLevel"), 0.0f);
            Component->SetFloatParameter(FName("DryLevel"), 1.0f);
            
            // Resetar parÃ¢metros adicionais
            Component->SetFloatParameter(FName("DelayWetLevel"), 0.0f);
            Component->SetFloatParameter(FName("DelayDryLevel"), 1.0f);
            
            ComponentsProcessed++;
        }
    }

    // Resetar estado atual
    CurrentDelayTime = 0.0f;
    CurrentDelayFeedback = 0.0f;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeito de delay removido de %d componentes"), ComponentsProcessed);
    
    return ComponentsProcessed > 0;
}

bool UAuracronAudioBridge::ApplyFrequencyFilter(float LowPassFreq, float HighPassFreq)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (LowPassFreq <= 0.0f || HighPassFreq <= 0.0f || HighPassFreq >= LowPassFreq)
    {
        return false;
    }

    // Aplicar filtros de frequÃªncia aos componentes ativos usando UE5.6 APIs nativas
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && !Component->bIsUISound)
        {
            // Aplicar Low Pass Filter usando API nativa do UE5.6
            Component->SetLowPassFilterFrequency(LowPassFreq);
            
            // Aplicar High Pass Filter se disponÃ­vel
            // Nota: SetHighPassFilterFrequency pode nÃ£o estar disponÃ­vel em todas as versÃµes
            // Usar parÃ¢metros MetaSound como fallback
            Component->SetFloatParameter(TEXT("HighPassFreq"), HighPassFreq);
            Component->SetFloatParameter(TEXT("LowPassFreq"), LowPassFreq);
            Component->SetBoolParameter(TEXT("EnableFiltering"), true);
            
            // Configurar parÃ¢metros adicionais para controle fino
            Component->SetFloatParameter(TEXT("FilterResonance"), 1.0f); // Q factor padrÃ£o
            Component->SetFloatParameter(TEXT("FilterGain"), 1.0f); // Ganho padrÃ£o
        }
    }

    // Armazenar configuraÃ§Ãµes atuais para controle posterior
    CurrentLowPassFreq = LowPassFreq;
    CurrentHighPassFreq = HighPassFreq;
    bFiltersEnabled = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Filtros de frequÃªncia UE5.6 aplicados: LP=%f Hz, HP=%f Hz"), LowPassFreq, HighPassFreq);
    return true;
}

// === Audio Configuration ===

bool UAuracronAudioBridge::ApplyAudioConfiguration(const FAuracronAudioConfiguration& NewConfig)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Validar configuraÃ§Ã£o
    if (!ValidateAudioConfiguration(NewConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ConfiguraÃ§Ã£o de Ã¡udio invÃ¡lida"));
        return false;
    }

    // Aplicar nova configuraÃ§Ã£o
    AudioConfiguration = NewConfig;

    // Atualizar volumes dos componentes ativos
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component))
        {
            float NewVolume = AudioConfiguration.MasterVolume;
            
            // Aplicar volume especÃ­fico baseado no tipo de som
            if (Component->bIsUISound)
            {
                NewVolume *= AudioConfiguration.UIVolume;
            }
            else
            {
                NewVolume *= AudioConfiguration.SFXVolume;
            }
            
            Component->SetVolumeMultiplier(NewVolume);
        }
    }

    // Atualizar mÃºsica atual se estiver tocando
    if (IsValid(CurrentMusicComponent))
    {
        float MusicVolume = AudioConfiguration.MasterVolume * AudioConfiguration.MusicVolume;
        CurrentMusicComponent->SetVolumeMultiplier(MusicVolume);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: ConfiguraÃ§Ã£o de Ã¡udio aplicada com sucesso"));
    return true;
}

bool UAuracronAudioBridge::SaveAudioSettings()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Usar GameUserSettings para salvar configuraÃ§Ãµes
    if (UGameUserSettings* UserSettings = UGameUserSettings::GetGameUserSettings())
    {
        // Salvar volumes como configuraÃ§Ãµes customizadas
        UserSettings->SetAudioQualityLevel(static_cast<int32>(AudioConfiguration.MasterVolume * 100));
        
        // Salvar configuraÃ§Ãµes adicionais usando SetCustomConfigValue se disponÃ­vel
        // Ou usar PlayerPrefs/SaveGame para configuraÃ§Ãµes customizadas
        
        UserSettings->SaveSettings();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: ConfiguraÃ§Ãµes de Ã¡udio salvas"));
        return true;
    }

    return false;
}

bool UAuracronAudioBridge::LoadAudioSettings()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Carregar configuraÃ§Ãµes do GameUserSettings
    if (UGameUserSettings* UserSettings = UGameUserSettings::GetGameUserSettings())
    {
        // Carregar volume master
        int32 SavedMasterVolume = UserSettings->GetAudioQualityLevel();
        AudioConfiguration.MasterVolume = FMath::Clamp(SavedMasterVolume / 100.0f, 0.0f, 1.0f);
        
        // Aplicar configuraÃ§Ã£o carregada
        ApplyAudioConfiguration(AudioConfiguration);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: ConfiguraÃ§Ãµes de Ã¡udio carregadas"));
        return true;
    }

    return false;
}

// === System Initialization ===

bool UAuracronAudioBridge::InitializeAudioSystem()
{
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de Ã¡udio jÃ¡ inicializado"));
        return true;
    }

    // Verificar se o mundo Ã© vÃ¡lido
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Mundo invÃ¡lido para inicializaÃ§Ã£o do sistema de Ã¡udio"));
        return false;
    }

    // Configurar MetaSounds
    if (!SetupMetaSounds())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar MetaSounds"));
        return false;
    }

    // Configurar mixing de Ã¡udio
    if (!SetupAudioMixing())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar mixing de Ã¡udio"));
        return false;
    }

    // Carregar configuraÃ§Ãµes salvas
    LoadAudioSettings();

    // Inicializar pool de componentes de Ã¡udio
    AudioComponentPool.Reserve(32); // Pool inicial de 32 componentes

    // Marcar sistema como inicializado
    bSystemInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Ã¡udio inicializado com sucesso"));
    return true;
}

bool UAuracronAudioBridge::SetupMetaSounds()
{
    // Verificar se MetaSounds estÃ£o disponÃ­veis
    if (!GetWorld())
    {
        return false;
    }

    // Configurar parÃ¢metros padrÃ£o para MetaSounds
    DefaultMetaSoundParameters.Add(TEXT("Volume"), 1.0f);
    DefaultMetaSoundParameters.Add(TEXT("Pitch"), 1.0f);
    DefaultMetaSoundParameters.Add(TEXT("ReverbSend"), 0.0f);
    
    // ParÃ¢metros de Delay para MetaSound Delay node
    DefaultMetaSoundParameters.Add(TEXT("DelayTime"), 0.0f); // Em segundos
    DefaultMetaSoundParameters.Add(TEXT("Feedback"), 0.0f);
    DefaultMetaSoundParameters.Add(TEXT("WetLevel"), 0.0f);
    DefaultMetaSoundParameters.Add(TEXT("DryLevel"), 1.0f);
    DefaultMetaSoundParameters.Add(TEXT("DelayWetLevel"), 0.0f);
    DefaultMetaSoundParameters.Add(TEXT("DelayDryLevel"), 1.0f);
    
    // ParÃ¢metros de Filtros
    DefaultMetaSoundParameters.Add(TEXT("LowPassFreq"), 20000.0f);
    DefaultMetaSoundParameters.Add(TEXT("HighPassFreq"), 20.0f);
    DefaultMetaSoundParameters.Add(TEXT("FilterResonance"), 1.0f);
    DefaultMetaSoundParameters.Add(TEXT("FilterGain"), 1.0f);
    DefaultMetaSoundParameters.Add(TEXT("EnableFiltering"), 0.0f); // Bool como float

    // Configurar parÃ¢metros especÃ­ficos para diferentes tipos de Ã¡udio
    ChampionVoiceParameters.Add(TEXT("VoiceIntensity"), 1.0f);
    ChampionVoiceParameters.Add(TEXT("EmotionalState"), 0.5f);
    
    AbilitySoundParameters.Add(TEXT("AbilityPower"), 1.0f);
    AbilitySoundParameters.Add(TEXT("ElementType"), 0.0f);
    
    MovementSoundParameters.Add(TEXT("MovementSpeed"), 1.0f);
    MovementSoundParameters.Add(TEXT("SurfaceType"), 0.0f);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: MetaSounds configurados com sucesso"));
    return true;
}

bool UAuracronAudioBridge::SetupAudioMixing()
{
    // Configurar submixes para diferentes categorias de Ã¡udio
    if (GetWorld())
    {
        // Configurar volumes padrÃ£o para diferentes categorias
        AudioConfiguration.MasterVolume = 1.0f;
        AudioConfiguration.MusicVolume = 0.7f;
        AudioConfiguration.SFXVolume = 0.8f;
        AudioConfiguration.VoiceVolume = 0.9f;
        AudioConfiguration.AmbientVolume = 0.6f;
        AudioConfiguration.UIVolume = 0.8f;

        // Configurar configuraÃ§Ãµes de mÃºsica dinÃ¢mica
        DynamicMusicConfig.bEnableDynamicMusic = true;
        DynamicMusicConfig.TransitionTime = 2.0f;
        DynamicMusicConfig.CrossfadeTime = 1.0f;
        DynamicMusicConfig.IntensityLevels = 5;
        DynamicMusicConfig.bEnableAdaptiveMusic = true;

        // Configurar SFX
        SFXConfig.bEnableRandomVariations = true;
        SFXConfig.MaxSimultaneousSounds = 16;
        SFXConfig.bEnableAudioComponentPooling = true;
        SFXConfig.PoolSize = 32;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Mixing de Ã¡udio configurado com sucesso"));
        return true;
    }

    return false;
}

// === Validation and Replication ===

bool UAuracronAudioBridge::ValidateAudioConfiguration(const FAuracronAudioConfiguration& Configuration) const
{
    // Validar volumes (devem estar entre 0.0 e 1.0)
    if (Configuration.MasterVolume < 0.0f || Configuration.MasterVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume master invÃ¡lido: %f"), Configuration.MasterVolume);
        return false;
    }

    if (Configuration.MusicVolume < 0.0f || Configuration.MusicVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume de mÃºsica invÃ¡lido: %f"), Configuration.MusicVolume);
        return false;
    }

    if (Configuration.SFXVolume < 0.0f || Configuration.SFXVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume de SFX invÃ¡lido: %f"), Configuration.SFXVolume);
        return false;
    }

    if (Configuration.VoiceVolume < 0.0f || Configuration.VoiceVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume de voz invÃ¡lido: %f"), Configuration.VoiceVolume);
        return false;
    }

    if (Configuration.AmbientVolume < 0.0f || Configuration.AmbientVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume ambiente invÃ¡lido: %f"), Configuration.AmbientVolume);
        return false;
    }

    if (Configuration.UIVolume < 0.0f || Configuration.UIVolume > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Volume de UI invÃ¡lido: %f"), Configuration.UIVolume);
        return false;
    }

    return true;
}

void UAuracronAudioBridge::OnRep_CurrentMusic()
{
    // Chamado quando CurrentMusic Ã© replicado
    if (IsValid(CurrentMusic))
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: MÃºsica atual replicada: %s"), *CurrentMusic->GetName());
        
        // Notificar mudanÃ§a de mÃºsica
        FString MusicName = CurrentMusic ? CurrentMusic->GetName() : TEXT("None");
        FString IntensityStr = FString::Printf(TEXT("%.2f"), CurrentMusicIntensity);
        OnMusicChanged.Broadcast(MusicName, IntensityStr);
        
        // Se nÃ£o estamos tocando a mÃºsica atual, iniciar reproduÃ§Ã£o
        if (!IsValid(CurrentMusicComponent) || CurrentMusicComponent->GetSound() != CurrentMusic)
        {
            FString MusicType = CurrentMusic ? CurrentMusic->GetName() : TEXT("Default");
            PlayDynamicMusic(MusicType, CurrentMusicIntensity > 0.5f);
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: MÃºsica atual removida via replicaÃ§Ã£o"));
        
        // Parar mÃºsica atual
        StopCurrentMusic();
    }
}

void UAuracronAudioBridge::OnRep_MusicIntensity()
{
    // Chamado quando CurrentMusicIntensity Ã© replicado
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Intensidade de mÃºsica replicada: %f"), CurrentMusicIntensity);
    
    // Aplicar nova intensidade Ã  mÃºsica atual
    if (IsValid(CurrentMusicComponent))
    {
        SetMusicIntensity(CurrentMusicIntensity);
    }
    
    // Notificar mudanÃ§a de mÃºsica com nova intensidade
    if (IsValid(CurrentMusic))
    {
        FString MusicName2 = CurrentMusic ? CurrentMusic->GetName() : TEXT("None");
        FString IntensityStr2 = FString::Printf(TEXT("%.2f"), CurrentMusicIntensity);
        OnMusicChanged.Broadcast(MusicName2, IntensityStr2);
    }
}

void UAuracronAudioBridge::OnAudioComponentFinished(UAudioComponent* AudioComponent)
{
    if (!AudioComponent || !IsValid(AudioComponent))
    {
        return;
    }
    
    // Remover da lista de componentes ativos
    ActiveAudioComponents.Remove(AudioComponent);
    
    // Retornar ao pool se estiver usando pooling e houver espaÃ§o
    if (SFXConfiguration.bUseAudioComponentPooling && 
        AudioComponentPool.Num() < SFXConfiguration.AudioComponentPoolSize)
    {
        // Limpar o delegate antes de retornar ao pool
        AudioComponent->OnAudioFinished.RemoveAll(this);
        
        // Resetar propriedades do componente
        AudioComponent->SetSound(nullptr);
        AudioComponent->SetVolumeMultiplier(1.0f);
        AudioComponent->SetPitchMultiplier(1.0f);
        AudioComponent->bAllowSpatialization = false;
        
        // Adicionar de volta ao pool
        AudioComponentPool.Add(AudioComponent);
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Componente de Ã¡udio retornado ao pool automaticamente"));
    }
    else
    {
        // Pool cheio ou pooling desabilitado, destruir componente
        AudioComponent->OnAudioFinished.RemoveAll(this);
        AudioComponent->DestroyComponent();
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Componente de Ã¡udio destruÃ­do (pool cheio ou desabilitado)"));
    }
}

bool UAuracronAudioBridge::PlayMetaSound(USoundBase* MetaSound, const FVector& Location, const TMap<FString, float>& Parameters)
{
    if (!bSystemInitialized || !MetaSound)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado ou MetaSound invÃ¡lido"));
        return false;
    }

    // Spawnar MetaSound
    // Skip MetaSound playback for now - needs proper MetaSound integration
    if (!MetaSound)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: MetaSound is null"));
        return false;
    }

    UAudioComponent* AudioComponent = UGameplayStatics::SpawnSoundAtLocation(
        GetWorld(),
        MetaSound,
        Location
    );

    if (AudioComponent)
    {
        // Aplicar parÃ¢metros do MetaSound
        for (const auto& Parameter : Parameters)
        {
            AudioComponent->SetFloatParameter(FName(*Parameter.Key), Parameter.Value);
        }

        // Configurar volume
        float FinalVolume = AudioConfiguration.SFXVolume * AudioConfiguration.MasterVolume;
        AudioComponent->SetVolumeMultiplier(FinalVolume);

        ActiveAudioComponents.Add(AudioComponent);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: MetaSound reproduzido com %d parÃ¢metros"), Parameters.Num());

        return true;
    }

    return false;
}

bool UAuracronAudioBridge::StopSound(UAudioComponent* AudioComponent)
{
    if (!AudioComponent || !IsValid(AudioComponent))
    {
        return false;
    }

    AudioComponent->Stop();
    
    // Remover da lista de ativos
    ActiveAudioComponents.Remove(AudioComponent);
    
    // Retornar ao pool se estiver usando pooling
    if (SFXConfiguration.bUseAudioComponentPooling && AudioComponentPool.Num() < SFXConfiguration.AudioComponentPoolSize)
    {
        AudioComponentPool.Add(AudioComponent);
    }
    else
    {
        AudioComponent->DestroyComponent();
    }

    return true;
}

bool UAuracronAudioBridge::PauseAllSounds()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && Component->IsPlaying())
        {
            Component->SetPaused(true);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Todos os sons pausados"));

    return true;
}

bool UAuracronAudioBridge::ResumeAllSounds()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && !Component->IsPlaying())
        {
            Component->SetPaused(false);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Todos os sons retomados"));

    return true;
}

// === Dynamic Music ===

bool UAuracronAudioBridge::PlayDynamicMusic(const FString& MusicType, bool bLoop)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado"));
        return false;
    }

    // Parar mÃºsica atual se estiver tocando
    if (!CurrentMusicType.IsEmpty() && CurrentMusicType != TEXT("None"))
    {
        StopCurrentMusic(true);
    }

    // Buscar mÃºsica no mapa de configuraÃ§Ã£o
    if (DynamicMusicConfiguration.MusicTracks.Contains(MusicType))
    {
        TSoftObjectPtr<USoundBase> MusicAsset = DynamicMusicConfiguration.MusicTracks[MusicType];
        
        if (MusicAsset.IsValid())
        {
            UAudioComponent* MusicComponent = UGameplayStatics::SpawnSound2D(
                GetWorld(),
                Cast<USoundBase>(MusicAsset.Get()),
                DynamicMusicConfiguration.DynamicMusicVolume * AudioConfiguration.MusicVolume * AudioConfiguration.MasterVolume
            );

            if (MusicComponent)
            {
                MusicComponent->bIsUISound = true;
                MusicComponent->bAutoDestroy = !bLoop;
                
                if (bLoop)
                {
                    MusicComponent->SetIntParameter(TEXT("Loop"), 1);
                }

                ActiveAudioComponents.Add(MusicComponent);
                CurrentMusicType = MusicType;

                // Broadcast evento de mudanÃ§a de mÃºsica
                OnMusicChanged.Broadcast(TEXT("None"), MusicType);

                UE_LOG(LogTemp, Log, TEXT("AURACRON: MÃºsica dinÃ¢mica iniciada: %s"), *MusicType);
                return true;
            }
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: MÃºsica nÃ£o encontrada: %s"), *MusicType);
    return false;
}

bool UAuracronAudioBridge::StopCurrentMusic(bool bFadeOut)
{
    if (!bSystemInitialized || CurrentMusicType.IsEmpty() || CurrentMusicType == TEXT("None"))
    {
        return false;
    }

    FString OldMusicType = CurrentMusicType;
    
    // Encontrar e parar componente de mÃºsica atual
    for (int32 i = ActiveAudioComponents.Num() - 1; i >= 0; i--)
    {
        UAudioComponent* Component = ActiveAudioComponents[i];
        if (IsValid(Component) && Component->bIsUISound)
        {
            if (bFadeOut)
            {
                Component->FadeOut(DynamicMusicConfiguration.MusicFadeTime, 0.0f);
            }
            else
            {
                Component->Stop();
            }
            
            ActiveAudioComponents.RemoveAt(i);
        }
    }

    CurrentMusicType = TEXT("None");
    
    // Broadcast evento de mudanÃ§a de mÃºsica
    OnMusicChanged.Broadcast(OldMusicType, TEXT("None"));

    UE_LOG(LogTemp, Log, TEXT("AURACRON: MÃºsica parada: %s"), *OldMusicType);
    return true;
}

bool UAuracronAudioBridge::TransitionToMusic(const FString& NewMusicType, float TransitionTime)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (DynamicMusicConfiguration.bUseAdaptiveTransitions && !CurrentMusicType.IsEmpty() && CurrentMusicType != TEXT("None"))
    {
        // Crossfade usando UE5.6 Audio Mixer com controle preciso de volume
        if (CurrentMusicComponent && IsValid(CurrentMusicComponent))
        {
            // Fade out mÃºsica atual usando Audio Mixer
            CurrentMusicComponent->FadeOut(TransitionTime, 0.0f, EAudioFaderCurve::Linear);
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando crossfade - fade out da mÃºsica atual: %s"), *CurrentMusicType);
        }
        
        // Carregar e preparar nova mÃºsica
        FString MusicPath = FString::Printf(TEXT("/Game/Audio/Music/%s"), *NewMusicType);
        USoundBase* NewMusic = LoadObject<USoundBase>(nullptr, *MusicPath);
        
        if (NewMusic)
        {
            // Criar novo componente para crossfade com volume inicial zero
            UAudioComponent* NewMusicComponent = UGameplayStatics::SpawnSound2D(
                GetWorld(),
                NewMusic,
                0.0f, // ComeÃ§ar com volume 0
                1.0f,
                0.0f,
                nullptr,
                false,
                true
            );
            
            if (NewMusicComponent)
            {
                // Fade in nova mÃºsica usando Audio Mixer com curva linear
                float TargetVolume = AudioConfiguration.MusicVolume * AudioConfiguration.MasterVolume * CurrentMusicIntensity;
                NewMusicComponent->FadeIn(TransitionTime, TargetVolume, 0.0f, EAudioFaderCurve::Linear);
                
                // Agendar limpeza da mÃºsica antiga
                FTimerHandle CleanupTimerHandle;
                GetWorld()->GetTimerManager().SetTimer(
                    CleanupTimerHandle,
                    [this, NewMusicComponent, NewMusicType]()
                    {
                        // Limpar mÃºsica antiga
                        if (CurrentMusicComponent && IsValid(CurrentMusicComponent))
                        {
                            CurrentMusicComponent->Stop();
                            ActiveAudioComponents.Remove(CurrentMusicComponent);
                        }
                        
                        // Atualizar referÃªncias
                        CurrentMusicComponent = NewMusicComponent;
                        CurrentMusicType = NewMusicType;
                        
                        // Disparar evento de mudanÃ§a de mÃºsica
                        OnMusicChanged.Broadcast(CurrentMusicType, NewMusicType);
                        
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: TransiÃ§Ã£o de mÃºsica concluÃ­da para: %s"), *NewMusicType);
                    },
                    TransitionTime,
                    false
                );
                
                ActiveAudioComponents.Add(NewMusicComponent);
                
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Iniciando crossfade para nova mÃºsica: %s"), *NewMusicType);
                return true;
            }
        }
        
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Falha ao carregar nova mÃºsica para transiÃ§Ã£o: %s"), *NewMusicType);
        // Fallback para transiÃ§Ã£o direta
        StopCurrentMusic(false);
        return PlayDynamicMusic(NewMusicType, true);
    }
    else
    {
        // TransiÃ§Ã£o direta sem crossfade
        StopCurrentMusic(false);
        return PlayDynamicMusic(NewMusicType, true);
    }

    return true;
}

bool UAuracronAudioBridge::SetMusicIntensity(float Intensity)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    CurrentMusicIntensity = FMath::Clamp(Intensity, 0.0f, 1.0f);

    // Aplicar intensidade aos componentes de mÃºsica ativos
    for (UAudioComponent* Component : ActiveAudioComponents)
    {
        if (IsValid(Component) && Component->bIsUISound)
        {
            Component->SetFloatParameter(TEXT("Intensity"), CurrentMusicIntensity);
            
            // Ajustar volume baseado na intensidade
            float IntensityVolume = FMath::Lerp(0.5f, 1.0f, CurrentMusicIntensity);
            float FinalVolume = DynamicMusicConfiguration.DynamicMusicVolume * AudioConfiguration.MusicVolume * AudioConfiguration.MasterVolume * IntensityVolume;
            Component->SetVolumeMultiplier(FinalVolume);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Intensidade da mÃºsica definida: %f"), CurrentMusicIntensity);
    return true;
}

// === Realm Audio ===

bool UAuracronAudioBridge::ChangeToRealmAudio(int32 RealmIndex)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema nÃ£o inicializado"));
        return false;
    }

    if (RealmIndex < 0 || RealmIndex >= 10) // Assumindo mÃ¡ximo de 10 realms
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ãndice de realm invÃ¡lido: %d"), RealmIndex);
        return false;
    }

    EAuracronAudioLayer OldLayer = AudioConfiguration.CurrentAudioLayer;
    
    // Mapear realm para layer de Ã¡udio
    EAuracronAudioLayer NewLayer = EAuracronAudioLayer::Surface;
    switch (RealmIndex)
    {
        case 0: case 1: case 2:
            NewLayer = EAuracronAudioLayer::Surface;
            break;
        case 3: case 4:
            NewLayer = EAuracronAudioLayer::Sky;
            break;
        case 5: case 6: case 7: case 8: case 9:
            NewLayer = EAuracronAudioLayer::Underground;
            break;
    }

    if (OldLayer != NewLayer)
    {
        AudioConfiguration.CurrentAudioLayer = NewLayer;
        
        // Aplicar transiÃ§Ã£o suave se habilitada
        if (AudioConfiguration.bUseSmoothLayerTransitions)
        {
            // Fade out sons da layer anterior
            for (UAudioComponent* Component : ActiveAudioComponents)
            {
                if (IsValid(Component) && !Component->bIsUISound)
                {
                    Component->FadeOut(AudioConfiguration.LayerTransitionTime * 0.5f, 0.0f);
                }
            }
        }

        // Aplicar efeitos especÃ­ficos do realm
        ApplyRealmAudioEffects(RealmIndex);
        
        // Broadcast evento de mudanÃ§a de layer
        OnAudioLayerChanged.Broadcast(OldLayer, NewLayer);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: MudanÃ§a para realm %d, layer: %d"), RealmIndex, (int32)NewLayer);
    }

    return true;
}

bool UAuracronAudioBridge::ApplyRealmAudioEffects(int32 RealmIndex)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Aplicar reverb baseado no realm
    FString ReverbType;
    float ReverbIntensity = 1.0f;
    
    switch (RealmIndex)
    {
        case 0: // Realm da SuperfÃ­cie
            ReverbType = TEXT("Forest");
            ReverbIntensity = 0.3f;
            break;
        case 1: // Realm do Fogo
            ReverbType = TEXT("Cave");
            ReverbIntensity = 0.7f;
            break;
        case 2: // Realm da Ãgua
            ReverbType = TEXT("Underwater");
            ReverbIntensity = 0.9f;
            break;
        case 3: // Realm do Ar
            ReverbType = TEXT("Sky");
            ReverbIntensity = 0.2f;
            break;
        case 4: // Realm da Terra
            ReverbType = TEXT("Mountain");
            ReverbIntensity = 0.5f;
            break;
        default: // Realms subterrÃ¢neos
            ReverbType = TEXT("Dungeon");
            ReverbIntensity = 0.8f;
            break;
    }

    ApplyReverbEffect(ReverbType, ReverbIntensity);

    // Aplicar filtros de frequÃªncia especÃ­ficos
    switch (AudioConfiguration.CurrentAudioLayer)
    {
        case EAuracronAudioLayer::Surface:
            ApplyFrequencyFilter(20000.0f, 20.0f); // Full spectrum
            break;
        case EAuracronAudioLayer::Sky:
            ApplyFrequencyFilter(15000.0f, 100.0f); // Menos graves
            break;
        case EAuracronAudioLayer::Underground:
            ApplyFrequencyFilter(8000.0f, 50.0f); // Mais abafado
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeitos de realm aplicados: %d"), RealmIndex);
    return true;
}

bool UAuracronAudioBridge::PlayRealmAmbientSound(int32 RealmIndex, const FVector& Location)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    if (RealmIndex < 0 || RealmIndex >= SFXConfiguration.RealmAmbientSounds.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Som ambiente de realm nÃ£o encontrado: %d"), RealmIndex);
        return false;
    }

    TSoftObjectPtr<USoundBase> AmbientSound = SFXConfiguration.RealmAmbientSounds[RealmIndex];
    
    if (AmbientSound.IsValid())
    {
        TMap<FString, float> Parameters;
        Parameters.Add(TEXT("Volume"), AudioConfiguration.AmbientVolume);
        Parameters.Add(TEXT("RealmIndex"), (float)RealmIndex);
        Parameters.Add(TEXT("Layer"), (float)AudioConfiguration.CurrentAudioLayer);
        
        bool bSuccess = PlayMetaSound(AmbientSound.Get(), Location, Parameters);
        
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Som ambiente de realm reproduzido: %d"), RealmIndex);
        }
        
        return bSuccess;
    }

    return false;
}

// ===== CALLBACK IMPLEMENTATIONS =====

// OnAudioComponentFinished implementation moved to avoid duplication

void UAuracronAudioBridge::OnAudioFinishedModern()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Audio finished (modern UE5.6 callback)"));

    // Handle modern audio finished callback
    // This is called when any audio component finishes playing
}

void UAuracronAudioBridge::Update3DAudio(float DeltaTime)
{
    if (!bSystemInitialized)
    {
        return;
    }

    // Update 3D audio positioning and effects
    // This function is called periodically to update spatial audio
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating 3D audio with DeltaTime: %f"), DeltaTime);

    // Implement 3D audio update logic using UE 5.6 APIs <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/API/Runtime/Engine/Components/UAudioComponent" index="1">1</mcreference>
    if (UWorld* World = GetWorld())
    {
        // Update listener position and orientation <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/spatialization-overview-in-unreal-engine" index="5">5</mcreference>
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            FVector ListenerLocation = PC->GetPawn() ? PC->GetPawn()->GetActorLocation() : FVector::ZeroVector;
            FRotator ListenerRotation = PC->GetControlRotation();
            
            // Update all active audio components
            for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
            {
                AActor* Actor = *ActorItr;
                if (UAudioComponent* AudioComp = Actor->FindComponentByClass<UAudioComponent>())
                {
                    // Apply distance attenuation <mcreference link="https://docs.unrealengine.com/5.1/en-US/API/Runtime/Engine/Components/UAudioComponent/" index="2">2</mcreference>
                    if (AudioComp->GetAttenuationSettingsToApply())
                    {
                        // Attenuation is automatically applied by the audio component in UE5
                        AudioComp->SetVolumeMultiplier(AudioComp->VolumeMultiplier);
                    }
                    
                    // Enable spatialization <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/python-api/class/AudioComponent?application_version=5.0" index="4">4</mcreference>
                    AudioComp->bAllowSpatialization = true;
                    
                    // Update reverb zones based on location
                    FVector SourceLocation = AudioComp->GetComponentLocation();
                    float Distance = FVector::Dist(ListenerLocation, SourceLocation);
                    
                    // Apply reverb based on distance and environment
                    if (Distance > 1000.0f) // Far distance reverb
                    {
                        AudioComp->SetLowPassFilterEnabled(true);
                        AudioComp->SetLowPassFilterFrequency(2000.0f);
                    }
                    else
                    {
                        AudioComp->SetLowPassFilterEnabled(false);
                    }
                }
            }
        }
    }
}

void UAuracronAudioBridge::ProcessAudioTransitions(float DeltaTime)
{
    if (!bSystemInitialized)
    {
        return;
    }

    // Process audio transitions and crossfades
    // This function handles smooth transitions between audio states
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Processing audio transitions with DeltaTime: %f"), DeltaTime);

    // Implement audio transition logic using UE 5.6 APIs <mcreference link="https://docs.unrealengine.com/5.0/en-US/API/Runtime/Engine/Components/UAudioComponent/FadeIn/" index="1">1</mcreference> <mcreference link="https://docs.unrealengine.com/5.1/en-US/API/Runtime/Engine/Components/UAudioComponent/FadeOut/" index="2">2</mcreference>
    if (UWorld* World = GetWorld())
    {
        // Process crossfades between tracks
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (UAudioComponent* AudioComp = Actor->FindComponentByClass<UAudioComponent>())
            {
                // Handle volume transitions based on audio state <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/API/Runtime/Engine/Components/UAudioComponent" index="5">5</mcreference>
                EAudioComponentPlayState PlayState = AudioComp->GetPlayState();
                
                if (PlayState == EAudioComponentPlayState::Playing)
                {
                    // Apply dynamic audio effects based on game state
                    // Note: GetVolumeMultiplier() was removed in UE 5.6, using VolumeMultiplier property instead
                    float CurrentVolume = AudioComp->VolumeMultiplier;
                    
                    // Example: Fade out distant sounds
                    if (APlayerController* PC = World->GetFirstPlayerController())
                    {
                        FVector ListenerLocation = PC->GetPawn() ? PC->GetPawn()->GetActorLocation() : FVector::ZeroVector;
                        FVector SourceLocation = AudioComp->GetComponentLocation();
                        float Distance = FVector::Dist(ListenerLocation, SourceLocation);
                        
                        // Crossfade based on distance <mcreference link="https://dev.epicgames.com/documentation/en-us/unreal-engine/sound-cue-reference-for-unreal-engine" index="4">4</mcreference>
                        if (Distance > 2000.0f && CurrentVolume > 0.1f)
                        {
                            // Fade out distant sounds
                            AudioComp->FadeOut(2.0f, 0.1f, EAudioFaderCurve::Linear);
                        }
                        else if (Distance <= 1000.0f && CurrentVolume < 1.0f)
                        {
                            // Fade in nearby sounds
                            AudioComp->FadeIn(1.5f, 1.0f, 0.0f, EAudioFaderCurve::Linear);
                        }
                        
                        // Apply volume adjustments for smooth transitions
                        AudioComp->AdjustVolume(DeltaTime, FMath::Clamp(1.0f - (Distance / 3000.0f), 0.1f, 1.0f), EAudioFaderCurve::Logarithmic);
                    }
                }
            }
        }
    }
}

#include "Modules/ModuleManager.h"

IMPLEMENT_MODULE(FDefaultModuleImpl, AuracronAudioBridge);
