// Configurações de Performance para AuracronCombatBridge
// Este arquivo define configurações ajustáveis para otimização de performance
// Permite balanceamento entre qualidade e performance em tempo de execução

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "AuracronCombatBridgeAPI.h"
#include "AuracronCombatPerformanceConfig.generated.h"

/**
 * Presets de performance predefinidos
 */
UENUM(BlueprintType)
enum class EPerformancePreset : uint8
{
    /** Máxima qualidade, performance pode ser afetada */
    Ultra       UMETA(DisplayName = "Ultra Quality"),
    
    /** Alta qualidade com boa performance */
    High        UMETA(DisplayName = "High Quality"),
    
    /** Qualidade balanceada */
    Medium      UMETA(DisplayName = "Medium Quality"),
    
    /** Prioriza performance sobre qualidade */
    Low         UMETA(DisplayName = "Low Quality"),
    
    /** Máxima performance, qualidade mínima */
    Performance UMETA(DisplayName = "Performance Mode")
};

/**
 * Configurações de performance para o sistema de combate Auracron
 * Permite ajustes dinâmicos para otimizar performance em diferentes cenários
 */
UCLASS(config = Game, defaultconfig, meta = (DisplayName = "Auracron Combat Performance"))
class AURACRONCOMBABRIDGE_API UAuracronCombatPerformanceConfig : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UAuracronCombatPerformanceConfig();

    // ========== CONFIGURAÇÕES DE CACHE ==========
    
    /** Intervalo de atualização do cache de alvos (segundos) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Cache", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float TargetCacheUpdateInterval = 0.5f;
    
    /** Duração do cache de alvos válidos (segundos) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Cache", meta = (ClampMin = "0.05", ClampMax = "1.0"))
    float TargetCacheDuration = 0.1f;
    
    /** Número máximo de alvos no cache */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Cache", meta = (ClampMin = "10", ClampMax = "500"))
    int32 MaxCachedTargets = 100;
    
    /** Intervalo para limpeza de cache expirado (segundos) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Cache", meta = (ClampMin = "1.0", ClampMax = "30.0"))
    float CacheCleanupInterval = 5.0f;
    
    // ========== CONFIGURAÇÕES DE PROCESSAMENTO PARALELO ==========
    
    /** Habilitar processamento paralelo para targeting */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Parallel Processing")
    bool bEnableParallelTargeting = true;
    
    /** Número mínimo de alvos para usar processamento paralelo */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Parallel Processing", meta = (ClampMin = "10", ClampMax = "100"))
    int32 MinTargetsForParallelProcessing = 20;
    
    /** Tamanho do batch para processamento paralelo */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Parallel Processing", meta = (ClampMin = "5", ClampMax = "50"))
    int32 ParallelProcessingBatchSize = 10;
    
    // ========== CONFIGURAÇÕES DE CACHE ADAPTATIVO ==========
    
    /** Habilitar cache adaptativo baseado na carga do sistema */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Adaptive Cache")
    bool bEnableAdaptiveCache = true;
    
    /** Threshold de frame time para reduzir qualidade (ms) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Adaptive Cache", meta = (ClampMin = "16.0", ClampMax = "100.0"))
    float HighFrameTimeThreshold = 33.0f; // 30 FPS
    
    /** Threshold de frame time para aumentar qualidade (ms) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Adaptive Cache", meta = (ClampMin = "8.0", ClampMax = "32.0"))
    float LowFrameTimeThreshold = 16.0f; // 60 FPS
    
    /** Fator de ajuste para cache adaptativo */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Adaptive Cache", meta = (ClampMin = "0.5", ClampMax = "2.0"))
    float AdaptiveCacheAdjustmentFactor = 1.5f;
    
    // ========== CONFIGURAÇÕES DE OTIMIZAÇÃO DE DISTÂNCIA ==========
    
    /** Usar distância ao quadrado para cálculos (mais rápido) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Distance Optimization")
    bool bUseSquaredDistance = true;
    
    /** Distância máxima para consideração de alvos (otimização) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Distance Optimization", meta = (ClampMin = "500.0", ClampMax = "10000.0"))
    float MaxTargetConsiderationDistance = 3000.0f;
    
    /** Usar sphere overlap em vez de iteração completa */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Distance Optimization")
    bool bUseSphereOverlapForTargeting = true;
    
    // ========== CONFIGURAÇÕES DE DEBUGGING E PROFILING ==========
    
    /** Habilitar logging de métricas de performance */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Debug")
    bool bEnablePerformanceLogging = false;
    
    /** Intervalo para logging de métricas (segundos) */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Debug", meta = (ClampMin = "1.0", ClampMax = "60.0"))
    float PerformanceLoggingInterval = 10.0f;
    
    /** Habilitar profiling detalhado */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Debug")
    bool bEnableDetailedProfiling = false;
    
    /** Mostrar estatísticas na tela */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Debug")
    bool bShowOnScreenStats = false;
    
    // ========== CONFIGURAÇÕES DE QUALIDADE POR PLATAFORMA ==========
    
    /** Configurações específicas para mobile */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Platform Specific")
    bool bUseMobileOptimizations = false;
    
    /** Redução de qualidade para plataformas de baixo desempenho */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Platform Specific", meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float LowEndPlatformQualityMultiplier = 0.7f;
    
    /** Configurações específicas para consoles */
    UPROPERTY(config, EditAnywhere, BlueprintReadOnly, Category = "Platform Specific")
    bool bUseConsoleOptimizations = true;
    
    // ========== MÉTODOS UTILITÁRIOS ==========
    
    /** Obter configurações otimizadas para a plataforma atual */
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void ApplyPlatformOptimizations();
    
    /** Resetar configurações para valores padrão */
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void ResetToDefaults();
    
    /** Aplicar preset de performance */
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void ApplyPerformancePreset(EPerformancePreset Preset);
    
    /** Validar configurações atuais */
    UFUNCTION(BlueprintCallable, Category = "Performance")
    bool ValidateSettings() const;

private:
    /** Aplicar configurações específicas da plataforma */
    void ApplyPlatformSpecificSettings();
    
    /** Detectar capacidades da plataforma */
    void DetectPlatformCapabilities();
};

/**
 * Estrutura para métricas de performance em tempo real
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronPerformanceMetrics
{
    GENERATED_BODY()

    /** Tempo médio de atualização do cache (ms) */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    float AverageCacheUpdateTime = 0.0f;
    
    /** Número de alvos processados por frame */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    int32 TargetsProcessedPerFrame = 0;
    
    /** Taxa de acerto do cache (%) */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    float CacheHitRate = 0.0f;
    
    /** Uso de memória do cache (KB) */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    float CacheMemoryUsage = 0.0f;
    
    /** Frame time atual (ms) */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    float CurrentFrameTime = 0.0f;
    
    /** Número de threads utilizados */
    UPROPERTY(BlueprintReadOnly, Category = "Metrics")
    int32 ActiveThreadCount = 0;
    
    FAuracronPerformanceMetrics()
    {
        AverageCacheUpdateTime = 0.0f;
        TargetsProcessedPerFrame = 0;
        CacheHitRate = 0.0f;
        CacheMemoryUsage = 0.0f;
        CurrentFrameTime = 0.0f;
        ActiveThreadCount = 0;
    }
};