Cmd: py "../../../../../../Aura/projeto/Auracron/Scripts/create_planicie_radiante_base.py"
LogPython: Script de criação da Planície Radiante - Auracron
LogPython: Versão: 1.0
LogPython: Unreal Engine 5.6 - Python API
LogPython: === Iniciando geração da Planície Radiante ===
LogPython: Configuração: 8km x 8km
LogPython: Elevação: 0m - 500m
LogPython: Resolução: 2017x2017
LogPython: [INFO] Inicializando integração com AuracronDynamicRealmSubsystem...
LogPython: [WARNING] Erro ao acessar via World: module 'unreal' has no attribute 'find_class'
LogPython: [WARNING] Erro ao acessar via GameInstance: module 'unreal' has no attribute 'get_game_instance'
LogPython: [WARNING] AuracronDynamicRealmSubsystem não encontrado - continuando sem integração
LogPython: [INFO] Inicializando AuracronRealmsBridge...
LogPython: [WARNING] AuracronMasterOrchestrator não disponível, tentando acesso direto
LogTemp: AURACRON: Configurações padrão dos Realms carregadas
LogPython: [WARNING] Erro ao configurar AuracronRealmsBridge: module 'unreal' has no attribute 'EAuracronRealmType'
LogPython: [WARNING] Erro ao ativar realm: AuracronRealmsBridge: Failed to convert parameter 'realm_type' when calling function 'AuracronRealmsBridge.ActivateRealm' on 'AuracronRealmsBridge_1'
  TypeError: NativizeProperty: Cannot nativize 'str' as 'RealmType' (EnumProperty)
    TypeError: NativizeEnumEntry: Cannot nativize 'str' as 'AuracronRealmType'
LogPython: [INFO] Configurando Data Layers...
LogPython: [WARNING] Método create_data_layer não disponível no bridge
LogPython: [INFO] Configurando Data Layers com método padrão...
LogPython: [WARNING] DataLayerSubsystem não disponível
LogPython: [ERROR] Nenhum subsistema de Data Layer disponível
LogPython: Aviso: Falha ao configurar Data Layers
LogPython: [SUCCESS] Material carregado: /Game/Materials/Landscape/M_PlanicieRadiante_Base
LogPython: [ERROR] Erro no AuracronWorldPartitionLandscapeManager: 'AuracronLandscapeConfiguration' object has no attribute 'size_x'
LogPython: [INFO] Usando método padrão de criação
LogPython: [INFO] Criando Landscape com método padrão...
LogPython: Gerando heightmap procedural...
LogPython: Heightmap gerado: 2017x2017 pixels, 8136578 bytes
LogPython: [WARNING] Falha ao usar create_landscape: module 'unreal' has no attribute 'find_class', tentando método alternativo
LogActorFactory: Actor Factory attempting to spawn Class /Script/Landscape.Landscape
LogActorFactory: Actor Factory attempting to spawn Class /Script/Landscape.Landscape
LogActorFactory: Actor Factory spawned Class /Script/Landscape.Landscape as actor: LandscapePlaceholder /Temp/Untitled_0.Untitled:PersistentLevel.LandscapePlaceholder_1
LogActorComponent: RegisterComponentWithWorld: (/Temp/Untitled_0.Untitled:PersistentLevel.LandscapePlaceholder_1.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogActorFactory: Actor Factory spawned Class /Script/Landscape.Landscape as actor: LandscapePlaceholder /Temp/Untitled_0.Untitled:PersistentLevel.LandscapePlaceholder_1
LogActorComponent: RegisterComponentWithWorld: (/Temp/Untitled_0.Untitled:PersistentLevel.LandscapePlaceholder_1.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogActorComponent: RegisterComponentWithWorld: (/Temp/Untitled_0.Untitled:PersistentLevel.LandscapePlaceholder_1.RootComponent0) Trying to register component with IsValid() == false. Aborting.
LogPython: [SUCCESS] Landscape fallback criado e escala aplicada: (1.00, 1.00, 1.00)
LogPython: [INFO] Landscape criado usando spawn_actor_from_class como fallback
LogPython: [WARNING] Erro ao verificar landscape: 'LandscapePlaceholder' object has no attribute 'is_valid'
LogPython: [INFO] Propriedades básicas do landscape configuradas
LogPython: [WARNING] Landscape proxy inválido, pulando registro de componentes
LogPython: [SUCCESS] Landscape criado: LandscapePlaceholder_1
LogPython: [ERROR] Realm Terrestrial não registrado
LogPython: [INFO] Configurando World Partition...
LogPython: [ERROR] Erro ao configurar World Partition: 'AuracronWorldPartitionPythonBridge' object has no attribute 'is_world_partition_enabled'
LogPython: [INFO] Tentando método padrão...
LogPython: [INFO] Configurando World Partition com método padrão...
LogPython: [ERROR] Erro ao configurar World Partition padrão: module 'unreal' has no attribute 'get_editor_world'
LogPython: Aviso: Falha ao configurar World Partition
LogPython: [INFO] Validando critérios de performance AAA...
LogPython: [PASS] Componentes (1024) dentro do limite para >60 FPS
LogPython: [PASS] Uso estimado de memória (31.0MB) dentro do limite de 4GB
LogPython: [FAIL] Tempo estimado de carregamento (32.0s) excede 30s
LogPython: [PASS] Resolução de heightmap (2017) otimizada para performance
LogPython: [PASS] Escala do landscape (0.0, 0.0) otimizada
LogPython: [WARNING] Alguns critérios de performance AAA não foram atendidos
LogPython: Aviso: Problemas de performance detectados
LogPython: [INFO] Executando testes automatizados AAA...
LogPython: [PASS] Teste 1/8: Landscape criado
LogPython: [PASS] Teste 2/8: Landscape válido: LandscapePlaceholder_1
LogPython: [PASS] Teste 3/8: Posição correta
LogPython: [FAIL] Teste 4/8: Escala incorreta. Esperado: 1.00, Atual: 0.00
LogPython: [INFO] Teste 5/8: Data Layer não configurado (opcional)
LogPython: [INFO] Teste 6/8: Sistema de realms não disponível (opcional)
LogPython: [INFO] Teste 7/8: Não foi possível verificar presença no mundo
LogPython: [PASS] Teste 8/8: Tamanho do terreno correto (8km x 8km)
LogPython: [INFO] Resultado dos testes: 6/8 (75.0%)
LogPython: [SUCCESS] Testes automatizados aprovados para qualidade AAA
LogPython: [WARNING] Erro ao salvar nível: module 'unreal' has no attribute 'get_editor_world'
LogPython: === Planície Radiante gerada com sucesso ===
LogPython: ✓ Script executado com sucesso!
LogPython: A Planície Radiante foi criada e está pronta para uso.
LogUObjectHash: Compacting FUObjectHashTables data took   0.90ms
Cmd: OBJ SAVEPACKAGE PACKAGE="/Temp/Untitled_0" FILE="../../../../../../Aura/projeto/Auracron/Saved/Autosaves/Temp/Untitled_0_Auto2.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
LogSavePackage: Moving output files for package: /Temp/Autosaves/Temp/Untitled_0_Auto2
LogSavePackage: Moving '../../../../../../Aura/projeto/Auracron/Saved/Untitled_0_Auto2EBC8A91046498C7E02B340B837BF4143.tmp' to '../../../../../../Aura/projeto/Auracron/Saved/Autosaves/Temp/Untitled_0_Auto2.umap'
LogFileHelpers: Editor autosave (incl. external actors) for '/Temp/Untitled_0' took 0.061
LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.061