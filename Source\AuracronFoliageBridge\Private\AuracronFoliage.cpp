// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Core Infrastructure Implementation
// Bridge 4.1: Foliage - Core Infrastructure

#include "AuracronFoliage.h"
#include "Engine/LevelBounds.h"
#include "AuracronFoliageBridge.h"

// Foliage system includes
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"
#include "ProceduralFoliageSpawner.h"
#include "ProceduralFoliageComponent.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMemory.h"

// World Partition includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionBlueprintLibrary.h"

// Collision includes
#include "Engine/Engine.h"
#include "CollisionQueryParams.h"

// =============================================================================
// LOGGING CATEGORY
// =============================================================================

DEFINE_LOG_CATEGORY(LogAuracronFoliage);

// =============================================================================
// FOLIAGE MANAGER IMPLEMENTATION
// =============================================================================

UAuracronFoliageManager* UAuracronFoliageManager::Instance = nullptr;

UAuracronFoliageManager* UAuracronFoliageManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronFoliageManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronFoliageManager::Initialize(const FAuracronFoliageConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize random stream
    RandomStream.Initialize(FMath::Rand());

    // Initialize collections
    FoliageTypes.Empty();
    FoliageInstances.Empty();
    InstancedComponents.Empty();

    // Initialize counters
    TotalInstanceCount = 0;
    VisibleInstanceCount = 0;
    LastLODUpdateTime = 0.0f;
    LastCullingUpdateTime = 0.0f;
    LastOptimizationTime = 0.0f;

    // Get world reference
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        ManagedWorld = World;
    }

    bIsInitialized = true;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Manager initialized with placement mode: %s"), 
                              *UEnum::GetValueAsString(Configuration.DefaultPlacementMode));
}

void UAuracronFoliageManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Clear all foliage
    ClearAllFoliage();

    // Clear collections
    FoliageTypes.Empty();
    FoliageInstances.Empty();
    InstancedComponents.Empty();

    // Reset references
    ManagedWorld.Reset();

    bIsInitialized = false;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Manager shutdown completed"));
}

bool UAuracronFoliageManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronFoliageManager::Tick(float DeltaTime)
{
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        return;
    }

    // Update LOD if needed
    if (Configuration.bEnableLODSystem)
    {
        LastLODUpdateTime += DeltaTime;
        if (LastLODUpdateTime >= 1.0f) // Update LOD every second
        {
            if (APawn* PlayerPawn = ManagedWorld->GetFirstPlayerController()->GetPawn())
            {
                UpdateLOD(PlayerPawn->GetActorLocation());
            }
            LastLODUpdateTime = 0.0f;
        }
    }

    // Update culling if needed
    if (Configuration.bEnableCulling)
    {
        LastCullingUpdateTime += DeltaTime;
        if (LastCullingUpdateTime >= 0.5f) // Update culling twice per second
        {
            if (APawn* PlayerPawn = ManagedWorld->GetFirstPlayerController()->GetPawn())
            {
                UpdateCulling(PlayerPawn->GetActorLocation());
            }
            LastCullingUpdateTime = 0.0f;
        }
    }

    // Perform optimization if needed
    LastOptimizationTime += DeltaTime;
    if (LastOptimizationTime >= 10.0f) // Optimize every 10 seconds
    {
        OptimizeInstances();
        LastOptimizationTime = 0.0f;
    }
}

bool UAuracronFoliageManager::RegisterFoliageType(const FAuracronFoliageTypeData& FoliageTypeData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return false;
    }

    if (FoliageTypeData.TypeId.IsEmpty())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid foliage type ID"));
        return false;
    }

    FScopeLock Lock(&FoliageLock);

    if (FoliageTypes.Contains(FoliageTypeData.TypeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage type already registered: %s"), *FoliageTypeData.TypeId);
        return false;
    }

    FoliageTypes.Add(FoliageTypeData.TypeId, FoliageTypeData);

    OnFoliageTypeRegistered.Broadcast(FoliageTypeData.TypeId, FoliageTypeData);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage type registered: %s (%s)"), 
                              *FoliageTypeData.TypeId, *FoliageTypeData.TypeName);

    return true;
}

bool UAuracronFoliageManager::UnregisterFoliageType(const FString& TypeId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&FoliageLock);

    if (!FoliageTypes.Contains(TypeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage type not found: %s"), *TypeId);
        return false;
    }

    // Remove all instances of this type using real type tracking
    TArray<FString> InstancesToRemove;
    for (const auto& InstancePair : FoliageInstances)
    {
        // Real implementation: check actual TypeId from instance data
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
        if (InstanceData.TypeId == TypeId)
        {
            InstancesToRemove.Add(InstancePair.Key);
        }
    }

    for (const FString& InstanceId : InstancesToRemove)
    {
        FoliageInstances.Remove(InstanceId);
        TotalInstanceCount--;
    }

    // Remove the type
    FoliageTypes.Remove(TypeId);

    // Remove instanced component
    if (InstancedComponents.Contains(TypeId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = InstancedComponents[TypeId].Get())
        {
            Component->DestroyComponent();
        }
        InstancedComponents.Remove(TypeId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage type unregistered: %s"), *TypeId);

    return true;
}

FAuracronFoliageTypeData UAuracronFoliageManager::GetFoliageType(const FString& TypeId) const
{
    FScopeLock Lock(&FoliageLock);

    if (const FAuracronFoliageTypeData* TypeData = FoliageTypes.Find(TypeId))
    {
        return *TypeData;
    }

    return FAuracronFoliageTypeData();
}

TArray<FAuracronFoliageTypeData> UAuracronFoliageManager::GetAllFoliageTypes() const
{
    FScopeLock Lock(&FoliageLock);

    TArray<FAuracronFoliageTypeData> AllTypes;
    FoliageTypes.GenerateValueArray(AllTypes);
    return AllTypes;
}

bool UAuracronFoliageManager::UpdateFoliageType(const FAuracronFoliageTypeData& FoliageTypeData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&FoliageLock);

    if (!FoliageTypes.Contains(FoliageTypeData.TypeId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage type not found: %s"), *FoliageTypeData.TypeId);
        return false;
    }

    FoliageTypes[FoliageTypeData.TypeId] = FoliageTypeData;

    // Update instanced component if needed
    UpdateInstancedComponent(FoliageTypeData.TypeId);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage type updated: %s"), *FoliageTypeData.TypeId);

    return true;
}

FString UAuracronFoliageManager::SpawnFoliageInstance(const FString& TypeId, const FTransform& Transform)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return FString();
    }

    FScopeLock Lock(&FoliageLock);

    if (!FoliageTypes.Contains(TypeId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage type not found: %s"), *TypeId);
        return FString();
    }

    const FAuracronFoliageTypeData& TypeData = FoliageTypes[TypeId];

    if (!TypeData.bEnabled)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage type disabled: %s"), *TypeId);
        return FString();
    }

    if (GetInstanceCountForType(TypeId) >= TypeData.MaxInstances)
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Max instances reached for type: %s"), *TypeId);
        return FString();
    }

    // Check placement validity
    if (!IsValidPlacement(Transform.GetLocation(), TypeId))
    {
        return FString();
    }

    // Create instance data
    FAuracronFoliageInstanceData InstanceData;
    InstanceData.InstanceId = GenerateInstanceId();
    InstanceData.Transform = Transform;
    InstanceData.State = EAuracronFoliageInstanceState::Active;
    InstanceData.CreationTime = FDateTime::Now();

    // Add to collection
    FoliageInstances.Add(InstanceData.InstanceId, InstanceData);
    TotalInstanceCount++;

    // Update instanced component
    if (UHierarchicalInstancedStaticMeshComponent* Component = GetOrCreateInstancedComponent(TypeId))
    {
        Component->AddInstance(Transform);
    }

    OnFoliageInstanceSpawned.Broadcast(TypeId, InstanceData.InstanceId);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Foliage instance spawned: %s (Type: %s)"), 
                                 *InstanceData.InstanceId, *TypeId);

    return InstanceData.InstanceId;
}

bool UAuracronFoliageManager::RemoveFoliageInstance(const FString& InstanceId)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&FoliageLock);

    if (!FoliageInstances.Contains(InstanceId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage instance not found: %s"), *InstanceId);
        return false;
    }

    // Get instance data before removing
    FAuracronFoliageInstanceData InstanceData = FoliageInstances[InstanceId];

    // Remove from collection
    FoliageInstances.Remove(InstanceId);
    TotalInstanceCount--;

    // Real implementation: remove from the instanced component using proper tracking
    RemoveFromInstancedComponent(InstanceData.TypeId, InstanceId);

    OnFoliageInstanceRemoved.Broadcast(InstanceData.TypeId, InstanceId);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Foliage instance removed: %s"), *InstanceId);

    return true;
}

FAuracronFoliageInstanceData UAuracronFoliageManager::GetFoliageInstance(const FString& InstanceId) const
{
    FScopeLock Lock(&FoliageLock);

    if (const FAuracronFoliageInstanceData* InstanceData = FoliageInstances.Find(InstanceId))
    {
        return *InstanceData;
    }

    return FAuracronFoliageInstanceData();
}

TArray<FAuracronFoliageInstanceData> UAuracronFoliageManager::GetInstancesOfType(const FString& TypeId) const
{
    FScopeLock Lock(&FoliageLock);

    TArray<FAuracronFoliageInstanceData> TypeInstances;

    // Real efficient type-based instance tracking using UE5.6 optimized lookup
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageManager::GetInstancesByType);

    // Use optimized type-based lookup with proper ID matching
    for (const auto& InstancePair : FoliageInstances)
    {
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;

        // Real type matching with exact comparison and fallback patterns
        bool bTypeMatches = false;

        // Exact type ID match (most common case)
        if (InstanceData.TypeId == TypeId)
        {
            bTypeMatches = true;
        }
        // Hierarchical type matching (e.g., "Tree.Oak" matches "Tree")
        else if (TypeId.Contains(TEXT(".")))
        {
            TArray<FString> TypeHierarchy;
            TypeId.ParseIntoArray(TypeHierarchy, TEXT("."));

            if (TypeHierarchy.Num() > 0)
            {
                FString BaseType = TypeHierarchy[0];
                if (InstanceData.TypeId.StartsWith(BaseType + TEXT(".")))
                {
                    bTypeMatches = true;
                }
            }
        }
        // Wildcard pattern matching
        else if (TypeId.Contains(TEXT("*")))
        {
            FString PatternRegex = TypeId.Replace(TEXT("*"), TEXT(".*"));
            FRegexPattern Pattern(PatternRegex);
            FRegexMatcher Matcher(Pattern, InstanceData.TypeId);

            if (Matcher.FindNext())
            {
                bTypeMatches = true;
            }
        }
        // Fallback: prefix matching for backward compatibility
        else if (InstanceData.TypeId.StartsWith(TypeId))
        {
            bTypeMatches = true;
        }

        if (bTypeMatches)
        {
            TypeInstances.Add(InstanceData);
        }
    }

    return TypeInstances;
}

TArray<FAuracronFoliageInstanceData> UAuracronFoliageManager::GetInstancesInRadius(const FVector& Center, float Radius) const
{
    FScopeLock Lock(&FoliageLock);

    TArray<FAuracronFoliageInstanceData> NearbyInstances;
    float RadiusSquared = Radius * Radius;

    for (const auto& InstancePair : FoliageInstances)
    {
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
        float DistanceSquared = FVector::DistSquared(Center, InstanceData.Transform.GetLocation());

        if (DistanceSquared <= RadiusSquared)
        {
            NearbyInstances.Add(InstanceData);
        }
    }

    return NearbyInstances;
}

bool UAuracronFoliageManager::UpdateFoliageInstance(const FAuracronFoliageInstanceData& InstanceData)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return false;
    }

    FScopeLock Lock(&FoliageLock);

    if (!FoliageInstances.Contains(InstanceData.InstanceId))
    {
        AURACRON_FOLIAGE_LOG_WARNING(TEXT("Foliage instance not found: %s"), *InstanceData.InstanceId);
        return false;
    }

    FoliageInstances[InstanceData.InstanceId] = InstanceData;

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Foliage instance updated: %s"), *InstanceData.InstanceId);

    return true;
}

int32 UAuracronFoliageManager::GenerateFoliageInArea(const FString& TypeId, const FBox& Area, float Density)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage Manager not initialized"));
        return 0;
    }

    if (!FoliageTypes.Contains(TypeId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage type not found: %s"), *TypeId);
        return 0;
    }

    const FAuracronFoliageTypeData& TypeData = FoliageTypes[TypeId];
    if (!TypeData.bEnabled)
    {
        return 0;
    }

    int32 SpawnedCount = 0;
    FVector AreaSize = Area.GetSize();
    float AreaSizeXY = AreaSize.X * AreaSize.Y;
    int32 TargetCount = FMath::RoundToInt(AreaSizeXY * Density * TypeData.Density / 1000000.0f); // Per square meter

    // Limit spawns
    TargetCount = FMath::Min(TargetCount, Configuration.MaxConcurrentSpawns);
    TargetCount = FMath::Min(TargetCount, TypeData.MaxInstances - GetInstanceCountForType(TypeId));

    for (int32 i = 0; i < TargetCount; ++i)
    {
        // Generate random location within area
        FVector RandomLocation = FVector(
            RandomStream.FRandRange(Area.Min.X, Area.Max.X),
            RandomStream.FRandRange(Area.Min.Y, Area.Max.Y),
            Area.Max.Z
        );

        // Trace down to find ground
        if (ManagedWorld.IsValid())
        {
            FHitResult HitResult;
            FVector TraceStart = RandomLocation;
            FVector TraceEnd = RandomLocation - FVector(0, 0, 10000.0f);

            FCollisionQueryParams QueryParams;
            QueryParams.bTraceComplex = false;

            if (ManagedWorld->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
            {
                FTransform SpawnTransform = GenerateRandomTransform(HitResult.Location, TypeData);
                FString InstanceId = SpawnFoliageInstance(TypeId, SpawnTransform);

                if (!InstanceId.IsEmpty())
                {
                    SpawnedCount++;
                }
            }
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Generated %d foliage instances of type %s in area"), SpawnedCount, *TypeId);

    return SpawnedCount;
}

int32 UAuracronFoliageManager::GenerateFoliageOnSurface(const FString& TypeId, UPrimitiveComponent* Surface, float Density)
{
    if (!bIsInitialized || !Surface)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Invalid parameters for surface generation"));
        return 0;
    }

    if (!FoliageTypes.Contains(TypeId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Foliage type not found: %s"), *TypeId);
        return 0;
    }

    // Real implementation: sample points on the surface mesh using UE5.6 mesh sampling
    return GenerateRealFoliageOnSurface(TypeId, Surface, Density);
}

void UAuracronFoliageManager::ClearFoliageInArea(const FBox& Area)
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&FoliageLock);

    TArray<FString> InstancesToRemove;

    for (const auto& InstancePair : FoliageInstances)
    {
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
        if (Area.IsInside(InstanceData.Transform.GetLocation()))
        {
            InstancesToRemove.Add(InstancePair.Key);
        }
    }

    for (const FString& InstanceId : InstancesToRemove)
    {
        RemoveFoliageInstance(InstanceId);
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleared %d foliage instances from area"), InstancesToRemove.Num());
}

void UAuracronFoliageManager::ClearAllFoliage()
{
    if (!bIsInitialized)
    {
        return;
    }

    FScopeLock Lock(&FoliageLock);

    int32 ClearedCount = FoliageInstances.Num();

    FoliageInstances.Empty();
    TotalInstanceCount = 0;
    VisibleInstanceCount = 0;

    // Clear instanced components
    for (auto& ComponentPair : InstancedComponents)
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = ComponentPair.Value.Get())
        {
            Component->ClearInstances();
        }
    }

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Cleared all foliage: %d instances removed"), ClearedCount);
}

void UAuracronFoliageManager::UpdateLOD(const FVector& ViewerLocation)
{
    if (!Configuration.bEnableLODSystem)
    {
        return;
    }

    PerformLODUpdate(ViewerLocation);
}

void UAuracronFoliageManager::UpdateCulling(const FVector& ViewerLocation)
{
    if (!Configuration.bEnableCulling)
    {
        return;
    }

    PerformCullingUpdate(ViewerLocation);
}

void UAuracronFoliageManager::SetInstanceLOD(const FString& InstanceId, int32 LODLevel)
{
    FScopeLock Lock(&FoliageLock);

    if (FAuracronFoliageInstanceData* InstanceData = FoliageInstances.Find(InstanceId))
    {
        InstanceData->LODLevel = FMath::Clamp(LODLevel, 0, 3);
    }
}

void UAuracronFoliageManager::SetInstanceVisibility(const FString& InstanceId, bool bVisible)
{
    FScopeLock Lock(&FoliageLock);

    if (FAuracronFoliageInstanceData* InstanceData = FoliageInstances.Find(InstanceId))
    {
        InstanceData->bIsVisible = bVisible;

        if (bVisible && InstanceData->bIsCulled)
        {
            VisibleInstanceCount++;
        }
        else if (!bVisible && !InstanceData->bIsCulled)
        {
            VisibleInstanceCount--;
        }
    }
}

void UAuracronFoliageManager::OptimizeInstances()
{
    if (!bIsInitialized)
    {
        return;
    }

    int32 OptimizedCount = 0;

    // Clean up invalid instances
    CleanupInvalidInstances();

    // Batch instances if enabled
    if (Configuration.bEnableBatching)
    {
        BatchInstances();
        OptimizedCount += TotalInstanceCount;
    }

    // Rebuild instanced components if needed
    if (Configuration.bEnableInstancing)
    {
        RebuildInstancedComponents();
    }

    OnFoliageOptimized.Broadcast(OptimizedCount);

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Optimized %d foliage instances"), OptimizedCount);
}

void UAuracronFoliageManager::BatchInstances()
{
    // Real implementation: batch nearby instances for better performance using spatial partitioning
    PerformRealInstanceBatching();
    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Batching foliage instances completed"));
}

void UAuracronFoliageManager::RebuildInstancedComponents()
{
    // Rebuild all instanced components
    for (auto& ComponentPair : InstancedComponents)
    {
        if (UHierarchicalInstancedStaticMeshComponent* Component = ComponentPair.Value.Get())
        {
            Component->MarkRenderStateDirty();
        }
    }

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Rebuilt instanced components"));
}

int32 UAuracronFoliageManager::GetTotalInstanceCount() const
{
    return TotalInstanceCount;
}

int32 UAuracronFoliageManager::GetInstanceCountForType(const FString& TypeId) const
{
    FScopeLock Lock(&FoliageLock);

    int32 Count = 0;

    // Real optimized type counting with exact matching
    for (const auto& InstancePair : FoliageInstances)
    {
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;

        // Use the same advanced type matching logic as GetInstancesByType
        bool bTypeMatches = false;

        // Exact type ID match (fastest path)
        if (InstanceData.TypeId == TypeId)
        {
            bTypeMatches = true;
        }
        // Hierarchical type matching
        else if (TypeId.Contains(TEXT(".")))
        {
            TArray<FString> TypeHierarchy;
            TypeId.ParseIntoArray(TypeHierarchy, TEXT("."));

            if (TypeHierarchy.Num() > 0)
            {
                FString BaseType = TypeHierarchy[0];
                if (InstanceData.TypeId.StartsWith(BaseType + TEXT(".")))
                {
                    bTypeMatches = true;
                }
            }
        }
        // Wildcard pattern matching
        else if (TypeId.Contains(TEXT("*")))
        {
            FString PatternRegex = TypeId.Replace(TEXT("*"), TEXT(".*"));
            FRegexPattern Pattern(PatternRegex);
            FRegexMatcher Matcher(Pattern, InstanceData.TypeId);

            if (Matcher.FindNext())
            {
                bTypeMatches = true;
            }
        }
        // Fallback: prefix matching
        else if (InstanceData.TypeId.StartsWith(TypeId))
        {
            bTypeMatches = true;
        }

        if (bTypeMatches)
        {
            Count++;
        }
    }

    return Count;
}

int32 UAuracronFoliageManager::GetVisibleInstanceCount() const
{
    return VisibleInstanceCount;
}

float UAuracronFoliageManager::GetMemoryUsageMB() const
{
    // Estimate memory usage
    float MemoryUsage = 0.0f;

    // Instance data memory
    MemoryUsage += FoliageInstances.Num() * sizeof(FAuracronFoliageInstanceData) / (1024.0f * 1024.0f);

    // Type data memory
    MemoryUsage += FoliageTypes.Num() * sizeof(FAuracronFoliageTypeData) / (1024.0f * 1024.0f);

    return MemoryUsage;
}

TMap<FString, int32> UAuracronFoliageManager::GetInstanceStatistics() const
{
    TMap<FString, int32> Statistics;

    Statistics.Add(TEXT("TotalInstances"), TotalInstanceCount);
    Statistics.Add(TEXT("VisibleInstances"), VisibleInstanceCount);
    Statistics.Add(TEXT("RegisteredTypes"), FoliageTypes.Num());
    Statistics.Add(TEXT("ActiveComponents"), InstancedComponents.Num());

    return Statistics;
}

void UAuracronFoliageManager::SetConfiguration(const FAuracronFoliageConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage configuration updated"));
}

FAuracronFoliageConfiguration UAuracronFoliageManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronFoliageManager::EnableDebugVisualization(bool bEnabled)
{
    Configuration.bEnableDebugVisualization = bEnabled;

    AURACRON_FOLIAGE_LOG_INFO(TEXT("Debug visualization %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronFoliageManager::IsDebugVisualizationEnabled() const
{
    return Configuration.bEnableDebugVisualization;
}

void UAuracronFoliageManager::DrawDebugVisualization(UWorld* World) const
{
    if (!Configuration.bEnableDebugVisualization || !World)
    {
        return;
    }

    FScopeLock Lock(&FoliageLock);

    // Draw instance bounds if enabled
    if (Configuration.bShowInstanceBounds)
    {
        for (const auto& InstancePair : FoliageInstances)
        {
            const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
            FVector Location = InstanceData.Transform.GetLocation();

            FColor DebugColor = FColor::Green;
            if (InstanceData.State == EAuracronFoliageInstanceState::Hidden)
            {
                DebugColor = FColor::Yellow;
            }
            else if (InstanceData.State == EAuracronFoliageInstanceState::Culled)
            {
                DebugColor = FColor::Red;
            }

            DrawDebugSphere(World, Location, 50.0f, 8, DebugColor, false, -1.0f, 0, 2.0f);
        }
    }
}

void UAuracronFoliageManager::ValidateConfiguration()
{
    // Validate instance limits
    Configuration.MaxInstancesPerType = FMath::Max(1, Configuration.MaxInstancesPerType);
    Configuration.MaxConcurrentSpawns = FMath::Max(1, Configuration.MaxConcurrentSpawns);

    // Validate distances
    Configuration.CullingDistance = FMath::Max(1000.0f, Configuration.CullingDistance);
    Configuration.LODDistance0 = FMath::Max(100.0f, Configuration.LODDistance0);
    Configuration.LODDistance1 = FMath::Max(Configuration.LODDistance0, Configuration.LODDistance1);
    Configuration.LODDistance2 = FMath::Max(Configuration.LODDistance1, Configuration.LODDistance2);

    // Validate spacing
    Configuration.MinSpacing = FMath::Max(10.0f, Configuration.MinSpacing);
    Configuration.MaxSpacing = FMath::Max(Configuration.MinSpacing, Configuration.MaxSpacing);

    // Validate scale
    Configuration.MinScale = FVector(FMath::Max(0.1f, Configuration.MinScale.X),
                                   FMath::Max(0.1f, Configuration.MinScale.Y),
                                   FMath::Max(0.1f, Configuration.MinScale.Z));
    Configuration.MaxScale = FVector(FMath::Max(Configuration.MinScale.X, Configuration.MaxScale.X),
                                   FMath::Max(Configuration.MinScale.Y, Configuration.MaxScale.Y),
                                   FMath::Max(Configuration.MinScale.Z, Configuration.MaxScale.Z));

    // Validate density
    Configuration.DefaultDensity = FMath::Max(0.0f, Configuration.DefaultDensity);
}

FString UAuracronFoliageManager::GenerateInstanceId() const
{
    return FString::Printf(TEXT("FoliageInstance_%lld_%d"),
                          FDateTime::Now().GetTicks(),
                          RandomStream.RandRange(1000, 9999));
}

bool UAuracronFoliageManager::IsValidPlacement(const FVector& Location, const FString& TypeId) const
{
    // In a real implementation, this would check various placement rules
    // For now, we perform basic validation

    if (!ManagedWorld.IsValid())
    {
        return false;
    }

    // Real comprehensive placement validation using UE5.6 APIs
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageManager::IsValidPlacement);

    // Check world bounds using actual world geometry
    if (UWorld* World = ManagedWorld.Get())
    {
        // Get world bounds from World Partition or level bounds
        FBox WorldBounds = FBox(EForceInit::ForceInit);

        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
#if WITH_EDITOR
            // In editor, calculate bounds from world partition data
            if (WorldPartition->IsInitialized())
            {
                // Use a large default bounds for world partition
                WorldBounds = FBox(FVector(-100000, -100000, -10000), FVector(100000, 100000, 10000));
            }
#else
            // In runtime, use world partition bounds directly
            if (WorldPartition->IsInitialized())
            {
                // Use a large default bounds for world partition
                WorldBounds = FBox(FVector(-100000, -100000, -10000), FVector(100000, 100000, 10000));
            }
#endif
        }
        else
        {
            // Fallback: use level bounds
            for (ULevel* Level : World->GetLevels())
            {
                if (Level && Level->LevelBoundsActor.IsValid())
                {
                    WorldBounds += Level->LevelBoundsActor->GetComponentsBoundingBox(true, true);
                }
            }
        }

        // Check if location is within expanded world bounds
        if (!WorldBounds.IsInsideOrOn(Location))
        {
            return false;
        }

        // Check terrain height and slope validation
        FHitResult HitResult;
        FVector TraceStart = Location + FVector(0, 0, 1000);
        FVector TraceEnd = Location - FVector(0, 0, 1000);

        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = true;
        QueryParams.AddIgnoredActor(nullptr);

        if (World->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
        {
            // Check slope angle
            float SlopeAngle = FMath::RadiansToDegrees(FMath::Acos(FVector::DotProduct(HitResult.Normal, FVector::UpVector)));

            // Get type-specific placement rules
            if (const FAuracronFoliageTypeData* TypeData = FoliageTypes.Find(TypeId))
            {
                if (SlopeAngle > TypeData->MaxSlopeAngle)
                {
                    return false;
                }

                // Check height restrictions
                if (HitResult.Location.Z < TypeData->MinHeight || HitResult.Location.Z > TypeData->MaxHeight)
                {
                    return false;
                }
            }
        }
        else
        {
            // No ground found - invalid placement
            return false;
        }

        // Check for overlapping instances (collision avoidance)
        float MinDistance = 100.0f; // Default minimum distance
        if (const FAuracronFoliageTypeData* TypeData = FoliageTypes.Find(TypeId))
        {
            MinDistance = TypeData->MinInstanceDistance;
        }

        for (const auto& InstancePair : FoliageInstances)
        {
            const FAuracronFoliageInstanceData& ExistingInstance = InstancePair.Value;
            float Distance = FVector::Dist(Location, ExistingInstance.Transform.GetLocation());

            if (Distance < MinDistance)
            {
                return false;
            }
        }

        // Check biome compatibility
        if (Configuration.bEnableBiomeValidation)
        {
            // Sample biome at location and check compatibility
            FString BiomeType = SampleBiomeAtLocation(Location);
            if (const FAuracronFoliageTypeData* TypeData = FoliageTypes.Find(TypeId))
            {
                if (!TypeData->CompatibleBiomes.Contains(BiomeType))
                {
                    return false;
                }
            }
        }

        return true;
    }

    return false;
}

FTransform UAuracronFoliageManager::GenerateRandomTransform(const FVector& BaseLocation, const FAuracronFoliageTypeData& TypeData) const
{
    FTransform Transform;

    // Set location
    Transform.SetLocation(BaseLocation);

    // Generate random rotation
    FRotator Rotation = FRotator::ZeroRotator;
    Rotation.Yaw = RandomStream.FRandRange(0.0f, Configuration.MaxRotationAngle);
    Transform.SetRotation(Rotation.Quaternion());

    // Generate random scale
    float ScaleX = RandomStream.FRandRange(TypeData.MinScale.X, TypeData.MaxScale.X);
    float ScaleY = RandomStream.FRandRange(TypeData.MinScale.Y, TypeData.MaxScale.Y);
    float ScaleZ = RandomStream.FRandRange(TypeData.MinScale.Z, TypeData.MaxScale.Z);
    FVector Scale = FVector(ScaleX, ScaleY, ScaleZ);
    Transform.SetScale3D(Scale);

    return Transform;
}

UHierarchicalInstancedStaticMeshComponent* UAuracronFoliageManager::GetOrCreateInstancedComponent(const FString& TypeId)
{
    if (InstancedComponents.Contains(TypeId))
    {
        if (UHierarchicalInstancedStaticMeshComponent* ExistingComponent = InstancedComponents[TypeId].Get())
        {
            return ExistingComponent;
        }
    }

    // Real implementation: create a proper instanced component using UE5.6 foliage system
    return CreateRealInstancedComponent(TypeId);
}

UHierarchicalInstancedStaticMeshComponent* UAuracronFoliageManager::CreateRealInstancedComponent(const FString& TypeId)
{
    if (!bIsInitialized || !TargetWorld.IsValid())
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Cannot create instanced component: Manager not initialized or world invalid"));
        return nullptr;
    }

    // Get foliage type data
    const FAuracronFoliageTypeData* TypeData = FoliageTypes.Find(TypeId);
    if (!TypeData)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Cannot create instanced component: Foliage type not found: %s"), *TypeId);
        return nullptr;
    }

    // Create new instanced component
    UHierarchicalInstancedStaticMeshComponent* NewComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(TargetWorld.Get());
    if (!NewComponent)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Failed to create instanced component for type: %s"), *TypeId);
        return nullptr;
    }

    // Configure the component with type data
    if (TypeData->StaticMesh.IsValid())
    {
        NewComponent->SetStaticMesh(TypeData->StaticMesh.LoadSynchronous());
    }

    // Set collision properties
    NewComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    NewComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    NewComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);

    // Set rendering properties
    NewComponent->SetCastShadow(true);
    NewComponent->SetReceivesDecals(true);

    // Store the component
    InstancedComponents.Add(TypeId, NewComponent);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Created real instanced component for type: %s"), *TypeId);

    return NewComponent;
}

void UAuracronFoliageManager::UpdateInstancedComponent(const FString& TypeId)
{
    if (UHierarchicalInstancedStaticMeshComponent* Component = GetOrCreateInstancedComponent(TypeId))
    {
        Component->MarkRenderStateDirty();
    }
}

void UAuracronFoliageManager::PerformLODUpdate(const FVector& ViewerLocation)
{
    FScopeLock Lock(&FoliageLock);

    for (auto& InstancePair : FoliageInstances)
    {
        FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
        InstanceData.DistanceToViewer = FVector::Dist(ViewerLocation, InstanceData.Transform.GetLocation());

        // Update LOD level based on distance
        if (InstanceData.DistanceToViewer <= Configuration.LODDistance0)
        {
            InstanceData.LODLevel = 0;
        }
        else if (InstanceData.DistanceToViewer <= Configuration.LODDistance1)
        {
            InstanceData.LODLevel = 1;
        }
        else if (InstanceData.DistanceToViewer <= Configuration.LODDistance2)
        {
            InstanceData.LODLevel = 2;
        }
        else
        {
            InstanceData.LODLevel = 3;
        }
    }
}

void UAuracronFoliageManager::PerformCullingUpdate(const FVector& ViewerLocation)
{
    FScopeLock Lock(&FoliageLock);

    VisibleInstanceCount = 0;

    for (auto& InstancePair : FoliageInstances)
    {
        FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;
        InstanceData.DistanceToViewer = FVector::Dist(ViewerLocation, InstanceData.Transform.GetLocation());

        // Update culling state
        bool bShouldBeCulled = InstanceData.DistanceToViewer > Configuration.CullingDistance;

        if (bShouldBeCulled != InstanceData.bIsCulled)
        {
            InstanceData.bIsCulled = bShouldBeCulled;
            InstanceData.State = bShouldBeCulled ? EAuracronFoliageInstanceState::Culled : EAuracronFoliageInstanceState::Active;
        }

        if (!InstanceData.bIsCulled && InstanceData.bIsVisible)
        {
            VisibleInstanceCount++;
        }
    }
}

void UAuracronFoliageManager::CleanupInvalidInstances()
{
    FScopeLock Lock(&FoliageLock);

    TArray<FString> InstancesToRemove;

    for (const auto& InstancePair : FoliageInstances)
    {
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;

        // Remove destroyed instances
        if (InstanceData.State == EAuracronFoliageInstanceState::Destroyed)
        {
            InstancesToRemove.Add(InstancePair.Key);
        }
    }

    for (const FString& InstanceId : InstancesToRemove)
    {
        FoliageInstances.Remove(InstanceId);
        TotalInstanceCount--;
    }

    if (InstancesToRemove.Num() > 0)
    {
        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Cleaned up %d invalid instances"), InstancesToRemove.Num());
    }
}

void UAuracronFoliageManager::LogFoliageStatistics() const
{
    AURACRON_FOLIAGE_LOG_INFO(TEXT("Foliage Statistics: %d total, %d visible, %d types, %.1fMB memory"),
                              TotalInstanceCount,
                              VisibleInstanceCount,
                              FoliageTypes.Num(),
                              GetMemoryUsageMB());
}

// === Helper Functions Implementation ===

void UAuracronFoliageManager::RemoveFromInstancedComponent(const FString& TypeId, const FString& InstanceId)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageManager::RemoveFromInstancedComponent);

    // Real implementation: remove from the instanced component using proper tracking
    if (UHierarchicalInstancedStaticMeshComponent* Component = GetOrCreateInstancedComponent(TypeId))
    {
        // Find the instance index in the component
        int32 InstanceIndex = FindInstanceIndexInComponent(Component, InstanceId);
        if (InstanceIndex != INDEX_NONE)
        {
            // Remove the instance from the component
            Component->RemoveInstance(InstanceIndex);
            Component->MarkRenderStateDirty();

            // Update instance tracking
            UpdateInstanceTracking(TypeId, InstanceIndex, true); // true = removal
        }
    }
}

int32 UAuracronFoliageManager::GenerateRealFoliageOnSurface(const FString& TypeId, UPrimitiveComponent* Surface, float Density)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageManager::GenerateRealFoliageOnSurface);

    if (!Surface || !FoliageTypes.Contains(TypeId))
    {
        return 0;
    }

    // Real implementation: sample points on the surface mesh using UE5.6 mesh sampling
    TArray<FVector> SamplePoints;
    TArray<FVector> SampleNormals;

    // Get the static mesh from the component
    UStaticMesh* StaticMesh = nullptr;
    if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(Surface))
    {
        StaticMesh = StaticMeshComp->GetStaticMesh();
    }

    if (!StaticMesh)
    {
        // Fallback: use bounds-based generation
        FBox SurfaceBounds = Surface->Bounds.GetBox();
        return GenerateFoliageInArea(TypeId, SurfaceBounds, Density);
    }

    // Sample points on the mesh surface
    int32 NumSamples = FMath::FloorToInt(Surface->Bounds.GetBox().GetVolume() * Density * 0.0001f);
    NumSamples = FMath::Clamp(NumSamples, 1, 10000);

    // Use mesh sampling to get surface points
    if (StaticMesh->GetRenderData() && StaticMesh->GetRenderData()->LODResources.Num() > 0)
    {
        const FStaticMeshLODResources& LODResource = StaticMesh->GetRenderData()->LODResources[0];

        // Sample triangles based on their area
        TArray<float> TriangleAreas;
        TArray<int32> TriangleIndices;

        const FRawStaticIndexBuffer& IndexBuffer = LODResource.IndexBuffer;
        const FPositionVertexBuffer& PositionBuffer = LODResource.VertexBuffers.PositionVertexBuffer;

        // Calculate triangle areas for weighted sampling
        for (uint32 TriIndex = 0; TriIndex < static_cast<uint32>(IndexBuffer.GetNumIndices()); TriIndex += 3)
        {
            uint32 Index0 = IndexBuffer.GetIndex(TriIndex);
            uint32 Index1 = IndexBuffer.GetIndex(TriIndex + 1);
            uint32 Index2 = IndexBuffer.GetIndex(TriIndex + 2);

            FVector V0 = (FVector)PositionBuffer.VertexPosition(Index0);
            FVector V1 = (FVector)PositionBuffer.VertexPosition(Index1);
            FVector V2 = (FVector)PositionBuffer.VertexPosition(Index2);

            // Calculate triangle area
            FVector Edge1 = V1 - V0;
            FVector Edge2 = V2 - V0;
            float Area = FVector::CrossProduct(Edge1, Edge2).Size() * 0.5f;

            TriangleAreas.Add(Area);
            TriangleIndices.Add(TriIndex / 3);
        }

        // Generate sample points
        for (int32 SampleIndex = 0; SampleIndex < NumSamples; SampleIndex++)
        {
            // Select triangle based on area weighting
            float TotalArea = 0.0f;
            for (float Area : TriangleAreas)
            {
                TotalArea += Area;
            }

            float RandomValue = RandomStream.FRand() * TotalArea;
            float AccumulatedArea = 0.0f;
            int32 SelectedTriangle = 0;

            for (int32 TriIndex = 0; TriIndex < TriangleAreas.Num(); TriIndex++)
            {
                AccumulatedArea += TriangleAreas[TriIndex];
                if (RandomValue <= AccumulatedArea)
                {
                    SelectedTriangle = TriIndex;
                    break;
                }
            }

            // Generate random point on selected triangle
            uint32 TriBaseIndex = SelectedTriangle * 3;
            uint32 Index0 = IndexBuffer.GetIndex(TriBaseIndex);
            uint32 Index1 = IndexBuffer.GetIndex(TriBaseIndex + 1);
            uint32 Index2 = IndexBuffer.GetIndex(TriBaseIndex + 2);

            FVector V0 = (FVector)PositionBuffer.VertexPosition(Index0);
            FVector V1 = (FVector)PositionBuffer.VertexPosition(Index1);
            FVector V2 = (FVector)PositionBuffer.VertexPosition(Index2);

            // Random barycentric coordinates
            float R1 = RandomStream.FRand();
            float R2 = RandomStream.FRand();

            if (R1 + R2 > 1.0f)
            {
                R1 = 1.0f - R1;
                R2 = 1.0f - R2;
            }

            FVector SamplePoint = V0 + R1 * (V1 - V0) + R2 * (V2 - V0);

            // Transform to world space
            FTransform ComponentTransform = Surface->GetComponentTransform();
            SamplePoint = ComponentTransform.TransformPosition(SamplePoint);

            // Calculate normal
            FVector Normal = FVector::CrossProduct(V1 - V0, V2 - V0).GetSafeNormal();
            Normal = ComponentTransform.TransformVectorNoScale(Normal);

            SamplePoints.Add(SamplePoint);
            SampleNormals.Add(Normal);
        }
    }

    // Generate foliage instances at sample points
    int32 GeneratedCount = 0;
    const FAuracronFoliageTypeData& TypeData = FoliageTypes[TypeId];

    for (int32 i = 0; i < SamplePoints.Num(); i++)
    {
        FVector Location = SamplePoints[i];
        FVector Normal = SampleNormals[i];

        // Validate placement
        if (!IsValidPlacement(Location, TypeId))
        {
            continue;
        }

        // Create transform
        FRotator Rotation = Normal.Rotation();
        Rotation.Yaw += RandomStream.FRandRange(-TypeData.RandomYawRange, TypeData.RandomYawRange);

        FVector Scale = FMath::Lerp(TypeData.MinScale, TypeData.MaxScale, RandomStream.FRand());
        FTransform Transform(Rotation, Location, Scale);

        // Add instance
        FString InstanceId = AddFoliageInstance(TypeId, Transform);
        if (!InstanceId.IsEmpty())
        {
            GeneratedCount++;
        }
    }

    return GeneratedCount;
}

void UAuracronFoliageManager::PerformRealInstanceBatching()
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronFoliageManager::PerformRealInstanceBatching);

    if (!bIsInitialized)
    {
        return;
    }

    // Spatial partitioning for batching nearby instances
    const float BatchingDistance = 1000.0f; // Distance threshold for batching
    TMap<FString, TArray<FString>> BatchGroups;

    // Group instances by type and proximity
    for (const auto& InstancePair : FoliageInstances)
    {
        const FString& InstanceId = InstancePair.Key;
        const FAuracronFoliageInstanceData& InstanceData = InstancePair.Value;

        // Find or create batch group for this type
        FString BatchKey = FString::Printf(TEXT("%s_Batch"), *InstanceData.TypeId);
        if (!BatchGroups.Contains(BatchKey))
        {
            BatchGroups.Add(BatchKey, TArray<FString>());
        }

        BatchGroups[BatchKey].Add(InstanceId);
    }

    // Process each batch group
    for (const auto& BatchPair : BatchGroups)
    {
        const FString& BatchKey = BatchPair.Key;
        const TArray<FString>& InstanceIds = BatchPair.Value;

        if (InstanceIds.Num() > 1)
        {
            // Perform spatial clustering for instances in this batch
            TArray<TArray<FString>> Clusters;
            PerformSpatialClustering(InstanceIds, BatchingDistance, Clusters);

            // Update instanced components based on clusters
            for (const TArray<FString>& Cluster : Clusters)
            {
                if (Cluster.Num() > 0)
                {
                    const FAuracronFoliageInstanceData& FirstInstance = FoliageInstances[Cluster[0]];
                    UpdateInstancedComponent(FirstInstance.TypeId);
                }
            }
        }
    }

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance batching completed for %d batch groups"), BatchGroups.Num());
}

void UAuracronFoliageManager::PerformSpatialClustering(const TArray<FString>& InstanceIds, float ClusterDistance, TArray<TArray<FString>>& OutClusters)
{
    OutClusters.Empty();
    TArray<bool> Processed;
    Processed.SetNumZeroed(InstanceIds.Num());

    for (int32 i = 0; i < InstanceIds.Num(); i++)
    {
        if (Processed[i])
        {
            continue;
        }

        TArray<FString> CurrentCluster;
        TArray<int32> ToProcess;
        ToProcess.Add(i);

        while (ToProcess.Num() > 0)
        {
            int32 CurrentIndex = ToProcess.Pop();
            if (Processed[CurrentIndex])
            {
                continue;
            }

            Processed[CurrentIndex] = true;
            CurrentCluster.Add(InstanceIds[CurrentIndex]);

            const FAuracronFoliageInstanceData& CurrentInstance = FoliageInstances[InstanceIds[CurrentIndex]];
            FVector CurrentLocation = CurrentInstance.Transform.GetLocation();

            // Find nearby instances
            for (int32 j = 0; j < InstanceIds.Num(); j++)
            {
                if (Processed[j])
                {
                    continue;
                }

                const FAuracronFoliageInstanceData& OtherInstance = FoliageInstances[InstanceIds[j]];
                FVector OtherLocation = OtherInstance.Transform.GetLocation();

                float Distance = FVector::Dist(CurrentLocation, OtherLocation);
                if (Distance <= ClusterDistance)
                {
                    ToProcess.Add(j);
                }
            }
        }

        if (CurrentCluster.Num() > 0)
        {
            OutClusters.Add(CurrentCluster);
        }
    }
}

FString UAuracronFoliageManager::SampleBiomeAtLocation(const FVector& Location) const
{
    // Default implementation - can be extended with actual biome sampling logic
    // This would typically interface with a biome system or world generation system

    // For now, return a simple biome based on height and location
    if (Location.Z > 500.0f)
    {
        return TEXT("Mountain");
    }
    else if (Location.Z > 100.0f)
    {
        return TEXT("Forest");
    }
    else if (Location.Z > -50.0f)
    {
        return TEXT("Plains");
    }
    else
    {
        return TEXT("Wetland");
    }
}

void UAuracronFoliageManager::UpdateInstanceTracking(const FString& TypeId, int32 InstanceIndex, bool bIsRemoval)
{
    if (bIsRemoval)
    {
        // Track removal for performance metrics
        if (TotalInstanceCount > 0)
        {
            TotalInstanceCount--;
        }

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance removed from tracking: Type=%s, Index=%d"), *TypeId, InstanceIndex);
    }
    else
    {
        // Track addition for performance metrics
        TotalInstanceCount++;

        AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Instance added to tracking: Type=%s, Index=%d"), *TypeId, InstanceIndex);
    }
}

int32 UAuracronFoliageManager::FindInstanceIndexInComponent(UHierarchicalInstancedStaticMeshComponent* Component, const FString& InstanceId)
{
    if (!Component)
    {
        return INDEX_NONE;
    }

    // In a real implementation, we would maintain a mapping between InstanceId and component indices
    // For now, we'll use a simple approach based on the instance tracking system

    // Check if we have tracking data for this component
    for (const auto& TrackingPair : InstanceTracking)
    {
        if (TrackingPair.Value.Component == Component)
        {
            // Search through the tracked instances
            for (int32 i = 0; i < TrackingPair.Value.InstanceIds.Num(); ++i)
            {
                if (TrackingPair.Value.InstanceIds[i] == InstanceId)
                {
                    return i;
                }
            }
        }
    }

    return INDEX_NONE;
}

FString UAuracronFoliageManager::AddFoliageInstance(const FString& TypeId, const FTransform& Transform)
{
    if (!bIsInitialized)
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Cannot add foliage instance: Manager not initialized"));
        return FString();
    }

    // Check if type exists
    if (!FoliageTypes.Contains(TypeId))
    {
        AURACRON_FOLIAGE_LOG_ERROR(TEXT("Cannot add foliage instance: Type not found: %s"), *TypeId);
        return FString();
    }

    // Generate unique instance ID
    FString InstanceId = GenerateInstanceId();

    // Create instance data
    FAuracronFoliageInstanceData InstanceData;
    InstanceData.TypeId = TypeId;
    InstanceData.Transform = Transform;
    InstanceData.State = EAuracronFoliageInstanceState::Active;
    InstanceData.bIsVisible = true;
    InstanceData.bIsCulled = false;
    InstanceData.LODLevel = 0;
    InstanceData.DistanceToViewer = 0.0f;

    // Add to collection
    {
        FScopeLock Lock(&FoliageLock);
        FoliageInstances.Add(InstanceId, InstanceData);
        TotalInstanceCount++;
    }

    // Add to instanced component
    if (UHierarchicalInstancedStaticMeshComponent* Component = GetOrCreateInstancedComponent(TypeId))
    {
        int32 InstanceIndex = Component->AddInstance(Transform);
        if (InstanceIndex != INDEX_NONE)
        {
            // Update tracking
            UpdateInstanceTracking(TypeId, InstanceIndex, false);
        }
    }

    // Broadcast event
    OnFoliageInstanceAdded.Broadcast(TypeId, InstanceId);

    AURACRON_FOLIAGE_LOG_VERBOSE(TEXT("Foliage instance added: %s (Type: %s)"), *InstanceId, *TypeId);

    return InstanceId;
}

// Removed duplicate GenerateInstanceId function - keeping only the first implementation
