// Implementação das configurações de performance para AuracronCombatBridge

#include "AuracronCombatPerformanceConfig.h"
#include "Engine/Engine.h"
#include "HAL/PlatformApplicationMisc.h"
#include "Misc/ConfigCacheIni.h"
#include "GenericPlatform/GenericPlatformMisc.h"

UAuracronCombatPerformanceConfig::UAuracronCombatPerformanceConfig()
{
    // Configurações padrão já definidas no header
    // Aplicar configurações específicas da plataforma na inicialização
    ApplyPlatformSpecificSettings();
}

void UAuracronCombatPerformanceConfig::ApplyPlatformOptimizations()
{
    DetectPlatformCapabilities();
    ApplyPlatformSpecificSettings();
    
    // Salvar configurações
    SaveConfig();
    
    UE_LOG(LogTemp, Log, TEXT("Auracron Combat Performance: Platform optimizations applied"));
}

void UAuracronCombatPerformanceConfig::ResetToDefaults()
{
    // Resetar para valores padrão
    TargetCacheUpdateInterval = 0.5f;
    TargetCacheDuration = 0.1f;
    MaxCachedTargets = 100;
    CacheCleanupInterval = 5.0f;
    
    bEnableParallelTargeting = true;
    MinTargetsForParallelProcessing = 20;
    ParallelProcessingBatchSize = 10;
    
    bEnableAdaptiveCache = true;
    HighFrameTimeThreshold = 33.0f;
    LowFrameTimeThreshold = 16.0f;
    AdaptiveCacheAdjustmentFactor = 1.5f;
    
    bUseSquaredDistance = true;
    MaxTargetConsiderationDistance = 3000.0f;
    bUseSphereOverlapForTargeting = true;
    
    bEnablePerformanceLogging = false;
    PerformanceLoggingInterval = 10.0f;
    bEnableDetailedProfiling = false;
    bShowOnScreenStats = false;
    
    bUseMobileOptimizations = false;
    LowEndPlatformQualityMultiplier = 0.7f;
    bUseConsoleOptimizations = true;
    
    // Aplicar configurações específicas da plataforma
    ApplyPlatformSpecificSettings();
    
    // Salvar configurações
    SaveConfig();
    
    UE_LOG(LogTemp, Log, TEXT("Auracron Combat Performance: Settings reset to defaults"));
}

void UAuracronCombatPerformanceConfig::ApplyPerformancePreset(EPerformancePreset Preset)
{
    switch (Preset)
    {
        case EPerformancePreset::Ultra:
            TargetCacheUpdateInterval = 0.1f;
            TargetCacheDuration = 0.05f;
            MaxCachedTargets = 200;
            bEnableParallelTargeting = true;
            MinTargetsForParallelProcessing = 10;
            bEnableAdaptiveCache = true;
            bUseSquaredDistance = false; // Usar distância real para máxima precisão
            MaxTargetConsiderationDistance = 5000.0f;
            bEnableDetailedProfiling = true;
            break;
            
        case EPerformancePreset::High:
            TargetCacheUpdateInterval = 0.3f;
            TargetCacheDuration = 0.1f;
            MaxCachedTargets = 150;
            bEnableParallelTargeting = true;
            MinTargetsForParallelProcessing = 15;
            bEnableAdaptiveCache = true;
            bUseSquaredDistance = true;
            MaxTargetConsiderationDistance = 4000.0f;
            bEnableDetailedProfiling = false;
            break;
            
        case EPerformancePreset::Medium:
            TargetCacheUpdateInterval = 0.5f;
            TargetCacheDuration = 0.15f;
            MaxCachedTargets = 100;
            bEnableParallelTargeting = true;
            MinTargetsForParallelProcessing = 20;
            bEnableAdaptiveCache = true;
            bUseSquaredDistance = true;
            MaxTargetConsiderationDistance = 3000.0f;
            bEnableDetailedProfiling = false;
            break;
            
        case EPerformancePreset::Low:
            TargetCacheUpdateInterval = 0.8f;
            TargetCacheDuration = 0.2f;
            MaxCachedTargets = 75;
            bEnableParallelTargeting = false;
            MinTargetsForParallelProcessing = 30;
            bEnableAdaptiveCache = true;
            bUseSquaredDistance = true;
            MaxTargetConsiderationDistance = 2500.0f;
            bEnableDetailedProfiling = false;
            break;
            
        case EPerformancePreset::Performance:
            TargetCacheUpdateInterval = 1.0f;
            TargetCacheDuration = 0.3f;
            MaxCachedTargets = 50;
            bEnableParallelTargeting = false;
            MinTargetsForParallelProcessing = 50;
            bEnableAdaptiveCache = false;
            bUseSquaredDistance = true;
            MaxTargetConsiderationDistance = 2000.0f;
            bEnableDetailedProfiling = false;
            bEnablePerformanceLogging = false;
            bShowOnScreenStats = false;
            break;
    }
    
    // Salvar configurações
    SaveConfig();
    
    UE_LOG(LogTemp, Log, TEXT("Auracron Combat Performance: Applied preset %d"), (int32)Preset);
}

bool UAuracronCombatPerformanceConfig::ValidateSettings() const
{
    bool bIsValid = true;
    
    // Validar intervalos de cache
    if (TargetCacheUpdateInterval < 0.1f || TargetCacheUpdateInterval > 2.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid TargetCacheUpdateInterval: %f"), TargetCacheUpdateInterval);
        bIsValid = false;
    }
    
    if (TargetCacheDuration < 0.05f || TargetCacheDuration > 1.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid TargetCacheDuration: %f"), TargetCacheDuration);
        bIsValid = false;
    }
    
    // Validar limites de alvos
    if (MaxCachedTargets < 10 || MaxCachedTargets > 500)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid MaxCachedTargets: %d"), MaxCachedTargets);
        bIsValid = false;
    }
    
    // Validar configurações de processamento paralelo
    if (MinTargetsForParallelProcessing < 10 || MinTargetsForParallelProcessing > 100)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid MinTargetsForParallelProcessing: %d"), MinTargetsForParallelProcessing);
        bIsValid = false;
    }
    
    if (ParallelProcessingBatchSize < 5 || ParallelProcessingBatchSize > 50)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid ParallelProcessingBatchSize: %d"), ParallelProcessingBatchSize);
        bIsValid = false;
    }
    
    // Validar thresholds de frame time
    if (HighFrameTimeThreshold <= LowFrameTimeThreshold)
    {
        UE_LOG(LogTemp, Warning, TEXT("HighFrameTimeThreshold must be greater than LowFrameTimeThreshold"));
        bIsValid = false;
    }
    
    // Validar distâncias
    if (MaxTargetConsiderationDistance < 500.0f || MaxTargetConsiderationDistance > 10000.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid MaxTargetConsiderationDistance: %f"), MaxTargetConsiderationDistance);
        bIsValid = false;
    }
    
    return bIsValid;
}

void UAuracronCombatPerformanceConfig::ApplyPlatformSpecificSettings()
{
    // Detectar plataforma atual usando macros de plataforma
    FString PlatformName;
    
#if PLATFORM_ANDROID
    PlatformName = TEXT("Android");
#elif PLATFORM_IOS
    PlatformName = TEXT("iOS");
#elif PLATFORM_WINDOWS
    PlatformName = TEXT("Windows");
#elif PLATFORM_MAC
    PlatformName = TEXT("Mac");
#elif PLATFORM_LINUX
    PlatformName = TEXT("Linux");
#elif PLATFORM_CONSOLE
    PlatformName = TEXT("Console");
#else
    PlatformName = TEXT("Unknown");
#endif
    
    // Configurações específicas para mobile
    if (PlatformName.Contains(TEXT("Android")) || PlatformName.Contains(TEXT("iOS")))
    {
        bUseMobileOptimizations = true;
        
        // Reduzir qualidade para mobile
        TargetCacheUpdateInterval *= 1.5f;
        TargetCacheDuration *= 1.2f;
        MaxCachedTargets = FMath::RoundToInt(MaxCachedTargets * 0.6f);
        MaxTargetConsiderationDistance *= 0.8f;
        
        // Desabilitar processamento paralelo em mobile por padrão
        bEnableParallelTargeting = false;
        
        UE_LOG(LogTemp, Log, TEXT("Applied mobile optimizations for platform: %s"), *PlatformName);
    }
    // Configurações para consoles
    else if (PlatformName.Contains(TEXT("XboxOne")) || PlatformName.Contains(TEXT("PS4")) || 
             PlatformName.Contains(TEXT("XSX")) || PlatformName.Contains(TEXT("PS5")) ||
             PlatformName.Contains(TEXT("Switch")))
    {
        bUseConsoleOptimizations = true;
        
        // Otimizações específicas para consoles
        if (PlatformName.Contains(TEXT("Switch")))
        {
            // Nintendo Switch tem limitações de performance
            TargetCacheUpdateInterval *= 1.3f;
            MaxCachedTargets = FMath::RoundToInt(MaxCachedTargets * 0.7f);
            bEnableParallelTargeting = false;
        }
        else
        {
            // PS5/Xbox Series X podem usar configurações mais agressivas
            if (PlatformName.Contains(TEXT("XSX")) || PlatformName.Contains(TEXT("PS5")))
            {
                TargetCacheUpdateInterval *= 0.8f;
                MaxCachedTargets = FMath::RoundToInt(MaxCachedTargets * 1.2f);
                bEnableParallelTargeting = true;
                MinTargetsForParallelProcessing = 15;
            }
        }
        
        UE_LOG(LogTemp, Log, TEXT("Applied console optimizations for platform: %s"), *PlatformName);
    }
    // Configurações para PC
    else
    {
        // Manter configurações padrão para PC, mas habilitar recursos avançados
        bEnableDetailedProfiling = true;
        bEnablePerformanceLogging = true;
        
        UE_LOG(LogTemp, Log, TEXT("Applied PC optimizations for platform: %s"), *PlatformName);
    }
}

void UAuracronCombatPerformanceConfig::DetectPlatformCapabilities()
{
    // Detectar número de cores da CPU
    int32 NumCores = FPlatformMisc::NumberOfCores();
    int32 NumWorkerThreads = FPlatformMisc::NumberOfWorkerThreadsToSpawn();
    
    // Ajustar configurações baseado no hardware
    if (NumCores >= 8)
    {
        // CPU de alta performance
        bEnableParallelTargeting = true;
        MinTargetsForParallelProcessing = 10;
        ParallelProcessingBatchSize = 15;
    }
    else if (NumCores >= 4)
    {
        // CPU de performance média
        bEnableParallelTargeting = true;
        MinTargetsForParallelProcessing = 20;
        ParallelProcessingBatchSize = 10;
    }
    else
    {
        // CPU de baixa performance
        bEnableParallelTargeting = false;
        MinTargetsForParallelProcessing = 50;
    }
    
    // Detectar memória disponível
    uint64 TotalPhysicalMemory = FPlatformMemory::GetConstants().TotalPhysicalGB;
    
    if (TotalPhysicalMemory >= 16)
    {
        // Memória abundante
        MaxCachedTargets = FMath::RoundToInt(MaxCachedTargets * 1.5f);
    }
    else if (TotalPhysicalMemory <= 4)
    {
        // Memória limitada
        MaxCachedTargets = FMath::RoundToInt(MaxCachedTargets * 0.6f);
        CacheCleanupInterval *= 0.5f; // Limpeza mais frequente
    }
    
    UE_LOG(LogTemp, Log, TEXT("Platform capabilities detected: %d cores, %d worker threads, %llu GB RAM"), 
           NumCores, NumWorkerThreads, TotalPhysicalMemory);
}