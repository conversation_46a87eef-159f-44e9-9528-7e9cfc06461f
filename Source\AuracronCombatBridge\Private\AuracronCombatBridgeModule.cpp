// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge Module Implementation

#include "AuracronCombatBridgeModule.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"

// Logs
DEFINE_LOG_CATEGORY_STATIC(LogAuracronCombatBridgeModule, Log, All);

#define LOCTEXT_NAMESPACE "FAuracronCombatBridgeModule"

void FAuracronCombatBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("AuracronCombatBridge module starting up..."));
    
    // Inicializar integrações com outros módulos
    InitializeModuleIntegrations();
    
    bModuleInitialized = true;
    
    UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("AuracronCombatBridge module started successfully"));
}

void FAuracronCombatBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("AuracronCombatBridge module shutting down..."));
    
    // Limpar integrações
    CleanupModuleIntegrations();
    
    bModuleInitialized = false;
    
    UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("AuracronCombatBridge module shut down successfully"));
}

bool FAuracronCombatBridgeModule::IsModuleLoaded()
{
    return FModuleManager::Get().IsModuleLoaded("AuracronCombatBridge");
}

FAuracronCombatBridgeModule& FAuracronCombatBridgeModule::Get()
{
    return FModuleManager::LoadModuleChecked<FAuracronCombatBridgeModule>("AuracronCombatBridge");
}

void FAuracronCombatBridgeModule::InitializeModuleIntegrations()
{
    // Verificar se os módulos dependentes estão carregados
    TArray<FString> RequiredModules = {
        TEXT("AuracronSigilosBridge"),
        TEXT("AuracronVerticalTransitionsBridge"),
        TEXT("AuracronRealmsBridge")
    };
    
    for (const FString& ModuleName : RequiredModules)
    {
        if (FModuleManager::Get().IsModuleLoaded(*ModuleName))
        {
            UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("Integration with %s module: OK"), *ModuleName);
        }
        else
        {
            UE_LOG(LogAuracronCombatBridgeModule, Warning, TEXT("Integration with %s module: NOT LOADED"), *ModuleName);
        }
    }
    
    // Verificar se o GameplayAbilities está disponível
    if (FModuleManager::Get().IsModuleLoaded("GameplayAbilities"))
    {
        UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("GameplayAbilities integration: OK"));
    }
    else
    {
        UE_LOG(LogAuracronCombatBridgeModule, Warning, TEXT("GameplayAbilities integration: NOT AVAILABLE"));
    }
}

void FAuracronCombatBridgeModule::CleanupModuleIntegrations()
{
    // Limpar qualquer integração específica se necessário
    UE_LOG(LogAuracronCombatBridgeModule, Log, TEXT("Module integrations cleaned up"));
}

#undef LOCTEXT_NAMESPACE

// Implementar a macro do módulo
IMPLEMENT_MODULE(FAuracronCombatBridgeModule, AuracronCombatBridge)