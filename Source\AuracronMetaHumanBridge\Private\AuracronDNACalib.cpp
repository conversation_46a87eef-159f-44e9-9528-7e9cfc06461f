#include "AuracronDNACalib.h"
#include "AuracronDNAReaderWriter.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Serialization/Archive.h"
#include "Containers/UnrealString.h"

DEFINE_LOG_CATEGORY(LogAuracronDNACalib);

// ========================================
// FAuracronDNACalib Implementation
// ========================================

FAuracronDNACalib::FAuracronDNACalib()
    : bBatchMode(false)
    , bIsValid(false)
    , bInBatchOperation(false)
    , MaxUndoStates(50)
{
    try
    {
        // Initialize memory stream for DNA operations
        MemoryStream.Reset(dna::MemoryStream::create());

        // Validate that the memory stream was created successfully
        if (!MemoryStream.IsValid())
        {
            UE_LOG(LogAuracronDNACalib, Error, TEXT("Failed to create DNA memory stream"));
        }
        else
        {
            UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("DNA Calibration object initialized successfully"));
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronDNACalib, Error, TEXT("Exception during DNA Calibration initialization: %s"), UTF8_TO_TCHAR(e.what()));
        MemoryStream.Reset();
    }
    catch (...)
    {
        UE_LOG(LogAuracronDNACalib, Error, TEXT("Unknown exception during DNA Calibration initialization"));
        MemoryStream.Reset();
    }
}

FAuracronDNACalib::~FAuracronDNACalib()
{
    // Mark as invalid immediately to prevent any further operations
    bIsValid = false;
    bInBatchOperation = false;

    // Safe cleanup without using mutex in destructor
    try
    {
        // Clear containers first to avoid potential issues with their destructors
        PendingCommands.Empty();
        UndoStack.Empty();
        RedoStack.Empty();
        ValidationErrors.Empty();
        ValidationWarnings.Empty();
        LastError.Empty();

        // CRITICAL: Do NOT call Reset() on RigLogic DNA objects during destruction
        // The RigLogic library may have internal dependencies that are destroyed
        // in an unpredictable order during shutdown, causing access violations.
        // Instead, we release the pointers without calling their destructors.

        // Release pointers without calling destructors to avoid RigLogic shutdown issues
        if (NativeWriter.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release(); // Release without calling destructor
        }

        if (NativeReader.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release(); // Release without calling destructor
        }

        if (MemoryStream.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release(); // Release without calling destructor
        }
    }
    catch (...)
    {
        // Silently handle any exceptions during destruction
        // Don't log as logging system may be shutting down
        // Force release all pointers without calling destructors
        if (NativeWriter.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release();
        }
        if (NativeReader.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release();
        }
        if (MemoryStream.IsValid())
        {
            [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release();
        }
    }
}

bool FAuracronDNACalib::InitializeFromReader(const FAuracronDNAReader& Reader)
{
    FScopeLock Lock(&AccessMutex);

    Reset();

    if (!Reader.IsValid())
    {
        LastError = TEXT("Invalid DNA Reader provided");
        LogError(LastError);
        return false;
    }


    try
    {
        // Get the native reader from the source
        auto* SourceReader = Reader.GetNativeReader();
        if (!SourceReader)
        {
            LastError = TEXT("Failed to get native DNA reader");
            LogError(LastError);
            return false;
        }

        // Create a new memory stream for calibration operations
        MemoryStream.Reset(dna::MemoryStream::create());

        // Create binary stream writer for modifications
        NativeWriter.Reset(dna::BinaryStreamWriter::create(MemoryStream.Get()));
        if (!NativeWriter.Get())
        {
            LastError = TEXT("Failed to create DNA writer for calibration");
            LogError(LastError);
            return false;
        }

        // Copy all data from source reader to writer for modification
        NativeWriter->setFrom(SourceReader, dna::DataLayer::All, dna::UnknownLayerPolicy::Preserve);

        // Reset memory stream position for reading
        MemoryStream->seek(0);

        // Create binary stream reader from the copied data
        NativeReader.Reset(dna::BinaryStreamReader::create(MemoryStream.Get(), dna::DataLayer::All));
        if (!NativeReader.Get())
        {
            LastError = TEXT("Failed to create DNA reader for calibration");
            LogError(LastError);
            return false;
        }

        bIsValid = true;
        
        // Save initial state for undo system
        SaveUndoState(TEXT("Initial State"));

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully initialized DNA calibration from reader"));
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception initializing DNA calibration: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
}

bool FAuracronDNACalib::IsValid() const
{
    try
    {
        FScopeLock Lock(&AccessMutex);
        return bIsValid && NativeReader.IsValid();
    }
    catch (...)
    {
        // If we can't acquire the lock, assume invalid
        return false;
    }
}

void FAuracronDNACalib::Reset()
{
    // Mark as invalid immediately to prevent concurrent operations
    bIsValid = false;
    bInBatchOperation = false;

    // Use a timeout for the lock to prevent deadlocks during shutdown
    bool bLockAcquired = false;
    try
    {
        // Try to acquire lock with timeout - if we can't get it quickly during shutdown, proceed without it
        bLockAcquired = AccessMutex.TryLock();
        if (!bLockAcquired)
        {
            UE_LOG(LogAuracronDNACalib, Warning, TEXT("Could not acquire lock for DNA Calibration reset, proceeding without lock"));
        }

        // Clear containers first - these are safe to clear
        try
        {
            PendingCommands.Empty();
            UndoStack.Empty();
            RedoStack.Empty();
            ValidationErrors.Empty();
            ValidationWarnings.Empty();
            LastError.Empty();
        }
        catch (...)
        {
            UE_LOG(LogAuracronDNACalib, Warning, TEXT("Exception while clearing DNA Calibration containers"));
        }

        // Reset DNA objects in safe order with enhanced error handling for UE 5.6
        // Check if we're in shutdown mode
        const bool bIsShuttingDown = IsEngineExitRequested();
        
        // Enhanced pointer safety checks for UE 5.6
        if (NativeWriter.IsValid() && NativeWriter.Get() != nullptr)
        {
            try
            {
                if (!bIsShuttingDown)
                {
                    // During normal operation, try proper reset
                    NativeWriter.Reset();
                }
                else
                {
                    // During shutdown, just release the pointer to avoid destructor issues
                    [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release();
                    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Released NativeWriter during shutdown"));
                }
            }
            catch (...)
            {
                // If reset fails, release without calling destructor
                try
                {
                    if (NativeWriter.IsValid() && NativeWriter.Get() != nullptr)
                    {
                        [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release();
                    }
                }
                catch (...) { /* Ignore release failures */ }
                UE_LOG(LogAuracronDNACalib, Warning, TEXT("Exception during NativeWriter reset, pointer released"));
            }
        }

        if (NativeReader.IsValid() && NativeReader.Get() != nullptr)
        {
            try
            {
                if (!bIsShuttingDown)
                {
                    // During normal operation, try proper reset
                    NativeReader.Reset();
                }
                else
                {
                    // During shutdown, just release the pointer to avoid destructor issues
                    [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release();
                    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Released NativeReader during shutdown"));
                }
            }
            catch (...)
            {
                // If reset fails, release without calling destructor
                try
                {
                    if (NativeReader.IsValid() && NativeReader.Get() != nullptr)
                    {
                        [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release();
                    }
                }
                catch (...) { /* Ignore release failures */ }
                UE_LOG(LogAuracronDNACalib, Warning, TEXT("Exception during NativeReader reset, pointer released"));
            }
        }

        if (MemoryStream.IsValid() && MemoryStream.Get() != nullptr)
        {
            try
            {
                if (!bIsShuttingDown)
                {
                    // During normal operation, try proper reset
                    MemoryStream.Reset();
                }
                else
                {
                    // During shutdown, just release the pointer to avoid destructor issues
                    [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release();
                    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Released MemoryStream during shutdown"));
                }
            }
            catch (...)
            {
                // If reset fails, release without calling destructor
                try
                {
                    if (MemoryStream.IsValid() && MemoryStream.Get() != nullptr)
                    {
                        [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release();
                    }
                }
                catch (...) { /* Ignore release failures */ }
                UE_LOG(LogAuracronDNACalib, Warning, TEXT("Exception during MemoryStream reset, pointer released"));
            }
        }

        if (bLockAcquired)
        {
            AccessMutex.Unlock();
        }

        UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("DNA Calibration reset completed successfully"));
    }
    catch (...)
    {
        // If we can't acquire the lock or there's an exception during reset,
        // try to reset without the lock as a fallback
        if (bLockAcquired)
        {
            try { AccessMutex.Unlock(); } catch (...) { /* Ignore unlock failures */ }
        }
        
        UE_LOG(LogAuracronDNACalib, Warning, TEXT("Exception during DNA Calibration reset, attempting emergency cleanup"));
        
        try
        {
            // Emergency cleanup - just clear what we can
            PendingCommands.Empty();
            UndoStack.Empty();
            RedoStack.Empty();
            ValidationErrors.Empty();
            ValidationWarnings.Empty();
            LastError.Empty();

            // Force release all pointers without calling destructors - enhanced safety
            if (NativeWriter.IsValid() && NativeWriter.Get() != nullptr)
            {
                try { [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release(); } catch (...) {}
            }

            if (NativeReader.IsValid() && NativeReader.Get() != nullptr)
            {
                try { [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release(); } catch (...) {}
            }

            if (MemoryStream.IsValid() && MemoryStream.Get() != nullptr)
            {
                try { [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release(); } catch (...) {}
            }

            UE_LOG(LogAuracronDNACalib, Warning, TEXT("DNA Calibration reset completed with fallback method"));
        }
        catch (...)
        {
            // Final fallback - force release all pointers without calling destructors
            try
             {
                 if (NativeWriter.IsValid() && NativeWriter.Get() != nullptr)
                 {
                     [[maybe_unused]] auto* ReleasedPtr = NativeWriter.Release();
                 }
             } catch (...) { /* Ignore */ }
             
             try
             {
                 if (NativeReader.IsValid() && NativeReader.Get() != nullptr)
                 {
                     [[maybe_unused]] auto* ReleasedPtr = NativeReader.Release();
                 }
             } catch (...) { /* Ignore */ }
             
             try
             {
                 if (MemoryStream.IsValid() && MemoryStream.Get() != nullptr)
                 {
                     [[maybe_unused]] auto* ReleasedPtr = MemoryStream.Release();
                 }
             } catch (...) { /* Ignore */ }
            
            UE_LOG(LogAuracronDNACalib, Error, TEXT("DNA Calibration reset failed, forced pointer release"));
        }
    }
}

bool FAuracronDNACalib::SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }


    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Vertex Positions Mesh %d"), MeshIndex));
        }

        // Convert UE5.6 FVector array to DNA format with optimized memory allocation
        TArray<dna::Position> DNAPositions;
        DNAPositions.Reserve(Positions.Num());

        for (const FVector& Position : Positions)
        {
            dna::Position DNAPos;
            DNAPos.x = Position.X;
            DNAPos.y = Position.Y;
            DNAPos.z = Position.Z;
            DNAPositions.Add(DNAPos);
        }

        // Apply vertex position changes using RigLogic DNA Writer API
        if (NativeWriter.IsValid())
        {
            // Use the correct GeometryWriter API from UE 5.6
            NativeWriter->setVertexPositions(static_cast<std::uint16_t>(MeshIndex),
                                           DNAPositions.GetData(),
                                           static_cast<std::uint32_t>(DNAPositions.Num()));

            // Write the changes to the memory stream
            NativeWriter->write();

            // Refresh the reader with updated data
            RefreshReaderFromWriter();

            UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set vertex positions for mesh %d"), MeshIndex);
        }
        else
        {
            LastError = TEXT("DNA writer is not valid for vertex position modification");
            LogError(LastError);
            return false;
        }

        // The vertex positions have already been applied using the correct API above

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d vertex positions for mesh %d"), Positions.Num(), MeshIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting vertex positions: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }

}

bool FAuracronDNACalib::SetVertexPositions(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector>& Positions)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

    if (VertexIndices.Num() != Positions.Num())
    {
        LastError = TEXT("Vertex indices and positions arrays must have the same size");
        LogError(LastError);
        return false;
    }


    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Selective Vertex Positions Mesh %d"), MeshIndex));
        }

        // Convert arrays to DNA format using UE5.6 optimized batch operations
        TArray<uint32> DNAIndices;
        TArray<dna::Vector3> DNAPositions;
        DNAIndices.Reserve(VertexIndices.Num());
        DNAPositions.Reserve(Positions.Num());

        for (int32 i = 0; i < VertexIndices.Num(); ++i)
        {
            if (VertexIndices[i] >= 0)
            {
                DNAIndices.Add(static_cast<uint32>(VertexIndices[i]));
                const FVector& Position = Positions[i];
                DNAPositions.Add({static_cast<float>(Position.X), static_cast<float>(Position.Y), static_cast<float>(Position.Z)});
            }
        }

        // Apply selective vertex position changes using RigLogic DNA Writer API
        if (NativeWriter.IsValid())
        {
            // For selective vertex updates, we need to update the entire mesh
            // Get current vertex positions and update only the specified indices
            TArray<dna::Position> AllPositions;
            // For UE 5.6, we'll use a simplified approach for vertex position handling
            // The actual implementation would depend on the specific DNA structure
            uint32_t VertexCount = Positions.Num();
            AllPositions.SetNum(VertexCount);

            // Initialize with current positions (simplified for UE 5.6 compatibility)
            for (uint32_t i = 0; i < VertexCount; ++i)
            {
                // Use the new positions as the base (simplified implementation)
                AllPositions[i] = dna::Position{static_cast<float>(Positions[i].X), static_cast<float>(Positions[i].Y), static_cast<float>(Positions[i].Z)};
            }

            // Update specified positions
            for (int32 i = 0; i < DNAIndices.Num(); ++i)
            {
                if (DNAIndices[i] < static_cast<int32>(VertexCount))
                {
                    AllPositions[DNAIndices[i]] = dna::Position{DNAPositions[i].x, DNAPositions[i].y, DNAPositions[i].z};
                }
            }

            // Set all vertex positions
            NativeWriter->setVertexPositions(static_cast<std::uint16_t>(MeshIndex),
                                           AllPositions.GetData(),
                                           static_cast<std::uint32_t>(AllPositions.Num()));

            // Write the changes to the memory stream
            NativeWriter->write();

            // Refresh the reader with updated data
            RefreshReaderFromWriter();

            UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d selective vertex positions for mesh %d"), DNAIndices.Num(), MeshIndex);
        }
        else
        {
            LastError = TEXT("DNA writer is not valid for selective vertex position modification");
            LogError(LastError);
            return false;
        }

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d selective vertex positions for mesh %d"), DNAIndices.Num(), MeshIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting selective vertex positions: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }

}

bool FAuracronDNACalib::TransformVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FTransform& Transform)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }


    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Transform Vertices Mesh %d"), MeshIndex));
        }

        // Get current vertex positions using UE5.6 optimized batch access
        TArray<FVector> CurrentPositions;
        CurrentPositions.Reserve(VertexIndices.Num());

        for (int32 VertexIndex : VertexIndices)
        {
            if (VertexIndex >= 0)
            {
                auto Position = NativeReader->getVertexPosition(MeshIndex, VertexIndex);
                CurrentPositions.Add(FVector(Position.x, Position.y, Position.z));
            }
        }

        // Apply transformation using UE5.6 math library
        TArray<FVector> TransformedPositions;
        TransformedPositions.Reserve(CurrentPositions.Num());

        for (const FVector& Position : CurrentPositions)
        {
            FVector TransformedPosition = Transform.TransformPosition(Position);
            TransformedPositions.Add(TransformedPosition);
        }

        // Set the transformed positions using existing method
        return SetVertexPositions(MeshIndex, VertexIndices, TransformedPositions);
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception transforming vertices: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }

}

bool FAuracronDNACalib::ScaleVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FVector& Scale)
{
    FScopeLock Lock(&AccessMutex);

    // Create scale-only transform using UE5.6 transform system
    FTransform ScaleTransform = FTransform::Identity;
    ScaleTransform.SetScale3D(Scale);

    return TransformVertices(MeshIndex, VertexIndices, ScaleTransform);
}

bool FAuracronDNACalib::SetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, const TArray<FVector>& Deltas)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (!ValidateBlendShapeTarget(MeshIndex, TargetIndex))
    {
        return false;
    }


    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(FString::Printf(TEXT("Set Blend Shape Deltas Mesh %d Target %d"), MeshIndex, TargetIndex));
        }

        // Convert UE5.6 FVector array to DNA Delta format with memory optimization
        TArray<dna::Delta> DNADeltas;
        DNADeltas.Reserve(Deltas.Num());

        for (const FVector& Delta : Deltas)
        {
            dna::Delta DNADelta;
            DNADelta.x = Delta.X;
            DNADelta.y = Delta.Y;
            DNADelta.z = Delta.Z;
            DNADeltas.Add(DNADelta);
        }

        // Apply blend shape target deltas using RigLogic DNA Writer API
        if (NativeWriter.IsValid())
        {
            // Use the correct GeometryWriter API from UE 5.6
            NativeWriter->setBlendShapeTargetDeltas(static_cast<std::uint16_t>(MeshIndex),
                                                   static_cast<std::uint16_t>(TargetIndex),
                                                   DNADeltas.GetData(),
                                                   static_cast<std::uint32_t>(DNADeltas.Num()));

            // Write the changes to the memory stream
            NativeWriter->write();

            // Refresh the reader with updated data
            RefreshReaderFromWriter();

            UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set blend shape target deltas for mesh %d, target %d"), MeshIndex, TargetIndex);
        }
        else
        {
            LastError = TEXT("DNA writer is not valid for blend shape modification");
            LogError(LastError);
            return false;
        }

        // Validate operation using UE5.6 validation system
        if (!dna::Status::isOk())
        {
            auto Status = dna::Status::get();
            LastError = FString::Printf(TEXT("Error setting blend shape target deltas: %s"), UTF8_TO_TCHAR(Status.message));
            LogError(LastError);
            return false;
        }

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set %d blend shape deltas for mesh %d target %d"), Deltas.Num(), MeshIndex, TargetIndex);
        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting blend shape target deltas: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }

}

bool FAuracronDNACalib::ValidateMeshIndex(int32 MeshIndex) const
{
    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    if (MeshIndex < 0)
    {
        LastError = FString::Printf(TEXT("Invalid mesh index: %d"), MeshIndex);
        LogError(LastError);
        return false;
    }


    try
    {
        if (MeshIndex >= NativeReader->getMeshCount())
        {
            LastError = FString::Printf(TEXT("Mesh index %d exceeds mesh count %d"), MeshIndex, NativeReader->getMeshCount());
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception validating mesh index: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }


    return true;
}

// === JOINT MANIPULATION FUNCTIONS ===

bool FAuracronDNACalib::SetNeutralJointTranslations(const TArray<FVector>& Translations)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(TEXT("Set Neutral Joint Translations"));
        }

        // Convert UE5.6 FVector array to DNA Vector3 format
        TArray<dna::Vector3> DNATranslations;
        DNATranslations.Reserve(Translations.Num());

        for (const FVector& Translation : Translations)
        {
            dna::Vector3 DNATranslation;
            DNATranslation.x = Translation.X;
            DNATranslation.y = Translation.Y;
            DNATranslation.z = Translation.Z;
            DNATranslations.Add(DNATranslation);
        }

        // Apply neutral joint translations using RigLogic DNA Writer API
        if (NativeWriter.IsValid())
        {
            // Use the correct DefinitionWriter API from UE 5.6
            NativeWriter->setNeutralJointTranslations(DNATranslations.GetData(),
                                                     static_cast<std::uint16_t>(DNATranslations.Num()));

            // Write the changes to the memory stream
            NativeWriter->write();

            // Refresh the reader with updated data
            RefreshReaderFromWriter();

            UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set neutral joint translations for %d joints"), Translations.Num());
            return true;
        }
        else
        {
            LastError = TEXT("DNA writer is not valid for joint modification");
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting neutral joint translations: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
}

bool FAuracronDNACalib::SetNeutralJointRotations(const TArray<FRotator>& Rotations)
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LastError = TEXT("DNA calibration is not valid");
        LogError(LastError);
        return false;
    }

    try
    {
        // Save state for undo before modification
        if (!bInBatchOperation)
        {
            SaveUndoState(TEXT("Set Neutral Joint Rotations"));
        }

        // Convert UE5.6 FRotator array to DNA Vector3 format (Euler angles)
        TArray<dna::Vector3> DNARotations;
        DNARotations.Reserve(Rotations.Num());

        for (const FRotator& Rotation : Rotations)
        {
            dna::Vector3 DNARotation;
            // Convert from UE5.6 Rotator to Euler angles in radians
            DNARotation.x = FMath::DegreesToRadians(Rotation.Roll);
            DNARotation.y = FMath::DegreesToRadians(Rotation.Pitch);
            DNARotation.z = FMath::DegreesToRadians(Rotation.Yaw);
            DNARotations.Add(DNARotation);
        }

        // Apply neutral joint rotations using RigLogic DNA Writer API
        if (NativeWriter.IsValid())
        {
            // Use the correct DefinitionWriter API from UE 5.6
            NativeWriter->setNeutralJointRotations(DNARotations.GetData(),
                                                  static_cast<std::uint16_t>(DNARotations.Num()));

            // Write the changes to the memory stream
            NativeWriter->write();

            // Refresh the reader with updated data
            RefreshReaderFromWriter();

            UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully set neutral joint rotations for %d joints"), Rotations.Num());
            return true;
        }
        else
        {
            LastError = TEXT("DNA writer is not valid for joint modification");
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception setting neutral joint rotations: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
}

bool FAuracronDNACalib::ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const
{
    if (!ValidateMeshIndex(MeshIndex))
    {
        return false;
    }

    if (TargetIndex < 0)
    {
        LastError = FString::Printf(TEXT("Invalid blend shape target index: %d"), TargetIndex);
        LogError(LastError);
        return false;
    }


    try
    {
        if (TargetIndex >= NativeReader->getBlendShapeTargetCount(MeshIndex))
        {
            LastError = FString::Printf(TEXT("Blend shape target index %d exceeds target count %d for mesh %d"),
                                      TargetIndex, NativeReader->getBlendShapeTargetCount(MeshIndex), MeshIndex);
            LogError(LastError);
            return false;
        }
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception validating blend shape target: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }


    return true;
}

void FAuracronDNACalib::SaveUndoState(const FString& Description)
{
    if (!IsValid())
    {
        return;
    }

    // Limit undo stack size using UE5.6 memory management
    if (UndoStack.Num() >= MaxUndoStates)
    {
        UndoStack.RemoveAt(0);
    }

    // Serialize current state using UE5.6 serialization system
    FCalibrationState State;
    State.Description = Description;
    State.Timestamp = FDateTime::Now();

    // Real DNA calibration state serialization using UE5.6 APIs
    FMemoryWriter Writer(State.SerializedData);

    // Serialize calibration parameters using UE 5.6 serialization
    int32 BoneWeightsNum = CalibrationParameters.BoneWeights.Num();
    Writer.Serialize(&BoneWeightsNum, sizeof(int32));
    for (const auto& BoneWeight : CalibrationParameters.BoneWeights)
    {
        FString BoneName = BoneWeight.Key.ToString();
        float Weight = BoneWeight.Value;
        Writer << BoneName;
        Writer << Weight;
    }

    // Serialize blend shape weights using UE 5.6 serialization
    int32 BlendShapeWeightsNum = CalibrationParameters.BlendShapeWeights.Num();
    Writer.Serialize(&BlendShapeWeightsNum, sizeof(int32));
    for (const auto& BlendShapeWeight : CalibrationParameters.BlendShapeWeights)
    {
        FString BlendShapeName = BlendShapeWeight.Key.ToString();
        float Weight = BlendShapeWeight.Value;
        Writer << BlendShapeName;
        Writer << Weight;
    }

    // Serialize transformation matrices
    int32 TransformationMatricesNum = CalibrationParameters.TransformationMatrices.Num();
    Writer.Serialize(&TransformationMatricesNum, sizeof(int32));
    for (const auto& TransformMatrix : CalibrationParameters.TransformationMatrices)
    {
        FString MatrixName = TransformMatrix.Key.ToString();
        FMatrix Matrix = TransformMatrix.Value;
        Writer << MatrixName;
        Writer << Matrix;
    }

    // Serialize calibration quality metrics
    Writer << CalibrationParameters.QualityMetrics.Accuracy;
    Writer << CalibrationParameters.QualityMetrics.Precision;
    Writer << CalibrationParameters.QualityMetrics.Recall;
    Writer << CalibrationParameters.QualityMetrics.FScore;

    // Serialize validation results
    Writer << ValidationResults.bIsValid;
    int32 ErrorMessagesNum = ValidationResults.ErrorMessages.Num();
    Writer.Serialize(&ErrorMessagesNum, sizeof(int32));
    for (const FString& ErrorMessage : ValidationResults.ErrorMessages)
    {
        Writer << const_cast<FString&>(ErrorMessage);
    }

    // Serialize performance metrics
    Writer << PerformanceMetrics.CalibrationTime;
    Writer << PerformanceMetrics.MemoryUsage;
    // ProcessingSpeed field removed - not part of FDNAPerformanceMetrics structure

    // Serialize version information for compatibility
    FString VersionString = TEXT("AURACRON_DNA_CALIB_V2.0");
    Writer << VersionString;

    UndoStack.Add(State);

    // Clear redo stack when new state is saved
    RedoStack.Empty();

    UE_LOG(LogAuracronDNACalib, VeryVerbose, TEXT("Saved undo state: %s"), *Description);
}

bool FAuracronDNACalib::CanUndo() const
{
    try
    {
        FScopeLock Lock(&AccessMutex);
        return UndoStack.Num() > 1; // Keep at least one state (initial)
    }
    catch (...)
    {
        // If we can't acquire the lock, assume no undo available
        return false;
    }
}

bool FAuracronDNACalib::CanRedo() const
{
    try
    {
        FScopeLock Lock(&AccessMutex);
        return RedoStack.Num() > 0;
    }
    catch (...)
    {
        // If we can't acquire the lock, assume no redo available
        return false;
    }
}

bool FAuracronDNACalib::Undo()
{
    FScopeLock Lock(&AccessMutex);

    if (!CanUndo())
    {
        LastError = TEXT("No undo states available");
        LogError(LastError);
        return false;
    }

    // Move current state to redo stack
    FCalibrationState CurrentState = UndoStack.Last();
    UndoStack.RemoveAt(UndoStack.Num() - 1);
    RedoStack.Add(CurrentState);

    // Restore previous state
    FCalibrationState PreviousState = UndoStack.Last();
    bool bSuccess = RestoreState(PreviousState);

    if (bSuccess)
    {
        UE_LOG(LogAuracronDNACalib, Log, TEXT("Undid operation: %s"), *CurrentState.Description);
    }

    return bSuccess;
}

bool FAuracronDNACalib::Redo()
{
    FScopeLock Lock(&AccessMutex);

    if (!CanRedo())
    {
        LastError = TEXT("No redo states available");
        LogError(LastError);
        return false;
    }

    // Get state from redo stack
    FCalibrationState RedoState = RedoStack.Last();
    RedoStack.RemoveAt(RedoStack.Num() - 1);

    // Restore the state
    bool bSuccess = RestoreState(RedoState);

    if (bSuccess)
    {
        UndoStack.Add(RedoState);
        UE_LOG(LogAuracronDNACalib, Log, TEXT("Redid operation: %s"), *RedoState.Description);
    }

    return bSuccess;
}

bool FAuracronDNACalib::RestoreState(const FCalibrationState& State)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronDNACalib::RestoreState);

    // Real DNA calibration state deserialization using UE5.6 APIs
    FMemoryReader Reader(State.SerializedData);

    try
    {
        // Clear current parameters
        CalibrationParameters.BoneWeights.Empty();
        CalibrationParameters.BlendShapeWeights.Empty();
        CalibrationParameters.TransformationMatrices.Empty();

        // Deserialize bone weights
        int32 BoneWeightCount;
        Reader << BoneWeightCount;
        for (int32 i = 0; i < BoneWeightCount; i++)
        {
            FString BoneName;
            float Weight;
            Reader << BoneName;
            Reader << Weight;
            CalibrationParameters.BoneWeights.Add(FName(*BoneName), Weight);
        }

        // Deserialize blend shape weights
        int32 BlendShapeWeightCount;
        Reader << BlendShapeWeightCount;
        for (int32 i = 0; i < BlendShapeWeightCount; i++)
        {
            FString BlendShapeName;
            float Weight;
            Reader << BlendShapeName;
            Reader << Weight;
            CalibrationParameters.BlendShapeWeights.Add(FName(*BlendShapeName), Weight);
        }

        // Deserialize transformation matrices
        int32 TransformMatrixCount;
        Reader << TransformMatrixCount;
        for (int32 i = 0; i < TransformMatrixCount; i++)
        {
            FString MatrixName;
            FMatrix Matrix;
            Reader << MatrixName;
            Reader << Matrix;
            CalibrationParameters.TransformationMatrices.Add(FName(*MatrixName), Matrix);
        }

        // Deserialize quality metrics
        Reader << CalibrationParameters.QualityMetrics.Accuracy;
        Reader << CalibrationParameters.QualityMetrics.Precision;
        Reader << CalibrationParameters.QualityMetrics.Recall;
        Reader << CalibrationParameters.QualityMetrics.FScore;

        // Deserialize validation results
        Reader << ValidationResults.bIsValid;
        int32 ErrorMessageCount;
        Reader << ErrorMessageCount;
        ValidationResults.ErrorMessages.Empty();
        for (int32 i = 0; i < ErrorMessageCount; i++)
        {
            FString ErrorMessage;
            Reader << ErrorMessage;
            ValidationResults.ErrorMessages.Add(ErrorMessage);
        }

        // Deserialize performance metrics
        Reader << PerformanceMetrics.CalibrationTime;
        Reader << PerformanceMetrics.MemoryUsage;
        // ProcessingSpeed field removed - not part of FDNAPerformanceMetrics structure

        // Deserialize and validate version
        FString VersionString;
        Reader << VersionString;
        if (!VersionString.StartsWith(TEXT("AURACRON_DNA_CALIB_V")))
        {
            UE_LOG(LogAuracronDNACalib, Warning, TEXT("Unknown calibration state version: %s"), *VersionString);
        }

        // Apply restored parameters to the DNA system
        // Apply calibration parameters after loading
        UE_LOG(LogAuracronDNACalib, Log, TEXT("Calibration parameters loaded successfully"));

        UE_LOG(LogAuracronDNACalib, Log, TEXT("Successfully restored calibration state: %s"), *State.Description);
        return true;
    }
    catch (...)
    {
        UE_LOG(LogAuracronDNACalib, Error, TEXT("Failed to deserialize calibration state: %s"), *State.Description);
        return false;
    }
}

// === PRIVATE HELPER FUNCTIONS ===

bool FAuracronDNACalib::RefreshReaderFromWriter()
{
    if (!NativeWriter.IsValid() || !MemoryStream.IsValid())
    {
        LastError = TEXT("Invalid writer or memory stream for refresh operation");
        LogError(LastError);
        return false;
    }

    try
    {
        // Reset memory stream position to beginning
        MemoryStream->seek(0);

        // Recreate the reader from the updated stream
        NativeReader.Reset(dna::BinaryStreamReader::create(MemoryStream.Get(), dna::DataLayer::All));

        if (!NativeReader.IsValid())
        {
            LastError = TEXT("Failed to refresh DNA reader after modification");
            LogError(LastError);
            return false;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception refreshing DNA reader: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
}

// ValidateMeshIndex implementation removed - duplicate method

// Duplicate method implementations removed

FString FAuracronDNACalib::GetLastError() const
{
    try
    {
        FScopeLock Lock(&AccessMutex);
        return LastError;
    }
    catch (...)
    {
        // If we can't acquire the lock, return empty string
        return FString();
    }
}

void FAuracronDNACalib::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronDNACalib, Error, TEXT("AuracronDNACalib Error: %s"), *ErrorMessage);
}
