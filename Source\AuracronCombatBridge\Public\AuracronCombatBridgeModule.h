// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge Module

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

/**
 * Módulo do Sistema de Combate 3D Vertical do Auracron
 * 
 * Este módulo coordena e integra os sistemas de combate existentes:
 * - AuracronSigilosBridge: Sistema de sígilos e fusão
 * - AuracronVerticalTransitionsBridge: Transições entre camadas
 * - AuracronRealmsBridge: Sistema de realms dinâmicos
 * 
 * Funcionalidades principais:
 * - Combate em 3 camadas verticais (Surface, Sky, Underground)
 * - Integração com sistema de sígilos para modificadores
 * - Cálculo de dano adaptativo baseado em posicionamento
 * - Sistema de targeting 3D com alcance variável
 * - Coordenação entre sistemas existentes sem duplicação
 */
class FAuracronCombatBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Verifica se o módulo está carregado */
    static bool IsModuleLoaded();
    
    /** Obtém referência ao módulo */
    static FAuracronCombatBridgeModule& Get();
    
private:
    /** Flag para indicar se o módulo foi inicializado */
    bool bModuleInitialized = false;
    
    /** Inicializar integrações com outros módulos */
    void InitializeModuleIntegrations();
    
    /** Limpar integrações */
    void CleanupModuleIntegrations();
};