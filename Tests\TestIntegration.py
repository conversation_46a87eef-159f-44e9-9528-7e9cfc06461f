#!/usr/bin/env python3
"""
TestIntegration.py - Teste de Integração AURACRON

Teste completo de integração para verificar se todos os sistemas fundamentais
do AURACRON estão funcionando corretamente e se comunicando entre si.

Autor: Sistema AURACRON
Versão: 1.0.0
Compatibilidade: Unreal Engine 5.6
"""

import sys
import os
import traceback
from pathlib import Path

# Adicionar o diretório Source ao path para importações
sys.path.insert(0, str(Path(__file__).parent.parent / "Source"))

try:
    # Importar todos os sistemas principais
    from Core.GameManager import AuracronGameManager
    from Core.EventSystem import AuracronEventSystem
    from Core.Logger import AuracronLogger
    from Core.ConfigManager import AuracronConfigManager
    from Core.ResourceManager import AuracronResourceManager
    from Core.TimeManager import AuracronTimeManager
    from Core.MathUtils import AuracronMathUtils, Vector3, Quaternion
    from Core.Constants import *
    
    print("✓ Todas as importações foram bem-sucedidas!")
except ImportError as e:
    print(f"✗ Erro de importação: {e}")
    traceback.print_exc()
    sys.exit(1)

def test_logger_system():
    """Testa o sistema de logging."""
    print("\n=== Testando Sistema de Logging ===")
    try:
        logger = AuracronLogger()
        logger.info("Teste de log INFO")
        logger.warning("Teste de log WARNING")
        logger.debug("Teste de log DEBUG")
        print("✓ Sistema de Logging funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no sistema de logging: {e}")
        return False

def test_config_manager():
    """Testa o gerenciador de configurações."""
    print("\n=== Testando Config Manager ===")
    try:
        config_manager = AuracronConfigManager()
        
        # Testar configuração padrão
        fps = config_manager.get('game.target_fps', 60)
        print(f"FPS alvo: {fps}")
        
        # Testar definição de configuração
        config_manager.set('test.value', 42)
        test_value = config_manager.get('test.value')
        assert test_value == 42, f"Esperado 42, obtido {test_value}"
        
        print("✓ Config Manager funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no config manager: {e}")
        return False

def test_event_system():
    """Testa o sistema de eventos."""
    print("\n=== Testando Sistema de Eventos ===")
    try:
        event_system = AuracronEventSystem()
        
        # Variável para testar callback
        test_result = {'called': False, 'data': None}
        
        def test_callback(data):
            test_result['called'] = True
            test_result['data'] = data
        
        # Registrar listener
        event_system.subscribe('test_event', test_callback)
        
        # Disparar evento
        event_system.emit('test_event', {'message': 'Hello World'})
        
        # Verificar se callback foi chamado
        assert test_result['called'], "Callback não foi chamado"
        assert test_result['data']['message'] == 'Hello World', "Dados incorretos"
        
        print("✓ Sistema de Eventos funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no sistema de eventos: {e}")
        return False

def test_math_utils():
    """Testa os utilitários matemáticos."""
    print("\n=== Testando Math Utils ===")
    try:
        # Testar Vector3
        v1 = Vector3(1, 2, 3)
        v2 = Vector3(4, 5, 6)
        v3 = v1 + v2
        assert v3.x == 5 and v3.y == 7 and v3.z == 9, "Soma de vetores incorreta"
        
        # Testar magnitude
        magnitude = v1.magnitude()
        expected = (1**2 + 2**2 + 3**2)**0.5
        assert abs(magnitude - expected) < 0.001, "Magnitude incorreta"
        
        # Testar Quaternion
        q1 = Quaternion(0, 0, 0, 1)
        q2 = Quaternion(0, 0, 0, 1)
        q3 = q1 * q2
        assert abs(q3.w - 1.0) < 0.001, "Multiplicação de quaternion incorreta"
        
        # Testar funções utilitárias
        lerp_result = AuracronMathUtils.lerp(0, 10, 0.5)
        assert abs(lerp_result - 5.0) < 0.001, "Lerp incorreto"
        
        print("✓ Math Utils funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no math utils: {e}")
        return False

def test_time_manager():
    """Testa o gerenciador de tempo."""
    print("\n=== Testando Time Manager ===")
    try:
        time_manager = AuracronTimeManager()
        
        # Testar configurações básicas
        time_manager.set_time_scale(2.0)
        assert time_manager.get_time_scale() == 2.0, "Time scale incorreto"
        
        # Testar pause/unpause
        time_manager.pause()
        assert time_manager.is_paused(), "Jogo deveria estar pausado"
        
        time_manager.unpause()
        assert not time_manager.is_paused(), "Jogo deveria estar despausado"
        
        print("✓ Time Manager funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no time manager: {e}")
        return False

def test_resource_manager():
    """Testa o gerenciador de recursos."""
    print("\n=== Testando Resource Manager ===")
    try:
        resource_manager = AuracronResourceManager()
        
        # Testar registro de recurso
        resource_manager.register_resource(
            "test_texture",
            "/Game/Textures/test.png",
            "TEXTURE"
        )
        
        # Verificar se foi registrado
        assert resource_manager.is_resource_registered("test_texture"), "Recurso não foi registrado"
        
        print("✓ Resource Manager funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no resource manager: {e}")
        return False

def test_game_manager():
    """Testa o gerenciador principal do jogo."""
    print("\n=== Testando Game Manager ===")
    try:
        game_manager = AuracronGameManager()
        
        # Testar inicialização
        game_manager.initialize()
        assert game_manager.is_initialized(), "Game Manager não foi inicializado"
        
        # Testar mudança de estado
        game_manager.change_state(GameState.MAIN_MENU)
        assert game_manager.get_current_state() == GameState.MAIN_MENU, "Estado incorreto"
        
        print("✓ Game Manager funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro no game manager: {e}")
        return False

def test_constants():
    """Testa as constantes globais."""
    print("\n=== Testando Constants ===")
    try:
        # Verificar se constantes existem
        assert hasattr(sys.modules[__name__], 'GAME_NAME'), "GAME_NAME não encontrado"
        assert hasattr(sys.modules[__name__], 'GAME_VERSION'), "GAME_VERSION não encontrado"
        assert hasattr(sys.modules[__name__], 'GameState'), "GameState não encontrado"
        
        # Verificar valores
        assert GAME_NAME == "AURACRON", f"Nome do jogo incorreto: {GAME_NAME}"
        assert isinstance(GAME_VERSION, str), "Versão deve ser string"
        
        print("✓ Constants funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro nas constants: {e}")
        return False

def test_integration():
    """Testa a integração entre sistemas."""
    print("\n=== Testando Integração entre Sistemas ===")
    try:
        # Criar instâncias dos sistemas
        logger = AuracronLogger()
        config_manager = AuracronConfigManager()
        event_system = AuracronEventSystem()
        game_manager = AuracronGameManager()
        
        # Testar comunicação entre sistemas via eventos
        integration_test = {'success': False}
        
        def on_game_state_change(data):
            logger.info(f"Estado do jogo mudou para: {data['new_state']}")
            integration_test['success'] = True
        
        event_system.subscribe('game_state_changed', on_game_state_change)
        
        # Simular mudança de estado
        event_system.emit('game_state_changed', {'new_state': 'PLAYING'})
        
        assert integration_test['success'], "Integração entre sistemas falhou"
        
        print("✓ Integração entre sistemas funcionando")
        return True
    except Exception as e:
        print(f"✗ Erro na integração: {e}")
        return False

def main():
    """Função principal do teste."""
    print("AURACRON - Teste de Integração")
    print("=" * 50)
    
    tests = [
        test_logger_system,
        test_config_manager,
        test_event_system,
        test_math_utils,
        test_time_manager,
        test_resource_manager,
        test_constants,
        test_game_manager,
        test_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Erro inesperado em {test.__name__}: {e}")
            traceback.print_exc()
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"RESULTADOS: {passed} passou(ram), {failed} falhou(falharam)")
    
    if failed == 0:
        print("🎉 TODOS OS TESTES PASSARAM! Sistema pronto para produção.")
        return 0
    else:
        print("❌ ALGUNS TESTES FALHARAM! Verifique os erros acima.")
        return 1

if __name__ == "__main__":
    sys.exit(main())