// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Living Canyon Generator
// Bridge 2.2: PCG Framework - Animated Geological Features

#pragma once

#include "CoreMinimal.h"
#include "AuracronPCGBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Animation/AnimSequence.h"
#include "Components/TimelineComponent.h"
#include "AuracronLivingCanyonPCG.generated.h"

/**
 * Living Canyon Breathing Configuration
 * Defines the breathing animation parameters for living canyons
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronLivingCanyonBreathingConfig
{
    GENERATED_BODY()

    /** Breathing cycle duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breathing", meta = (ClampMin = "5.0", ClampMax = "60.0"))
    float BreathingCycleDuration = 20.0f;

    /** Maximum expansion amount */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breathing", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float MaxExpansion = 1.5f;

    /** Breathing intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breathing", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float BreathingIntensity = 1.0f;

    /** Enable organic pulsing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breathing")
    bool bEnableOrganicPulsing = true;

    /** Pulse frequency variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Breathing", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float PulseVariation = 0.3f;
};

/**
 * Living Canyon Configuration Structure
 * Defines properties for individual living canyon generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronLivingCanyonConfig
{
    GENERATED_BODY()

    /** Canyon center location */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Location")
    FVector CenterLocation = FVector::ZeroVector;

    /** Canyon length */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "1000.0", ClampMax = "10000.0"))
    float CanyonLength = 3000.0f;

    /** Canyon width */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "200.0", ClampMax = "2000.0"))
    float CanyonWidth = 800.0f;

    /** Canyon depth */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "100.0", ClampMax = "1000.0"))
    float CanyonDepth = 400.0f;

    /** Canyon orientation angle */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Orientation", meta = (ClampMin = "0.0", ClampMax = "360.0"))
    float OrientationAngle = 0.0f;

    /** Breathing configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    FAuracronLivingCanyonBreathingConfig BreathingConfig;

    /** Canyon wall material color */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Appearance")
    FLinearColor WallColor = FLinearColor(0.6f, 0.4f, 0.3f, 1.0f);

    /** Enable bioluminescent veins */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bEnableBioluminescence = true;

    /** Bioluminescent vein color */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    FLinearColor BioluminescentColor = FLinearColor(0.2f, 0.8f, 0.3f, 1.0f);

    /** Enable ambient sounds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bEnableAmbientSounds = true;
};

/**
 * Auracron Living Canyon PCG Settings
 * Generates animated canyon formations with breathing effects
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Geological")
class AURACRONPCGBRIDGE_API UAuracronLivingCanyonPCGSettings : public UAuracronTerrainPCGSettings
{
    GENERATED_BODY()

public:
    UAuracronLivingCanyonPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Array of living canyon configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Canyons")
    TArray<FAuracronLivingCanyonConfig> CanyonConfigurations;

    /** Canyon wall mesh */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UStaticMesh> CanyonWallMesh;

    /** Canyon floor mesh */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UStaticMesh> CanyonFloorMesh;

    /** Living canyon material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UMaterialInterface> LivingCanyonMaterial;

    /** Breathing animation curve */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    TSoftObjectPtr<UCurveFloat> BreathingCurve;

    /** Enable performance optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceOptimization = true;

    /** Animation LOD distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "15000.0"))
    float AnimationLODDistance = 8000.0f;

    /** Maximum animated segments per canyon */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "10", ClampMax = "200"))
    int32 MaxAnimatedSegments = 50;

    /** Integration with VFX Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseVFXBridge = true;

    /** Integration with Audio Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseAudioBridge = true;

    /** Integration with Dynamic Realm Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseDynamicRealm = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Living Canyon PCG Element
 * Handles the actual generation of living canyons with breathing animations
 */
class AURACRONPCGBRIDGE_API FAuracronLivingCanyonPCGElement : public FAuracronTerrainPCGElement
{
public:
    FAuracronLivingCanyonPCGElement() = default;
    virtual ~FAuracronLivingCanyonPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    
    /** Generate canyon base geometry */
    void GenerateCanyonGeometry(FPCGContext* Context, const UAuracronLivingCanyonPCGSettings* Settings, 
                              const FAuracronLivingCanyonConfig& Config, FPCGDataCollection& OutputData) const;
    
    /** Generate canyon walls with breathing capability */
    void GenerateBreathingWalls(FPCGContext* Context, const UAuracronLivingCanyonPCGSettings* Settings,
                               const FAuracronLivingCanyonConfig& Config, FPCGDataCollection& OutputData) const;
    
    /** Setup breathing animation system */
    void SetupBreathingAnimation(FPCGContext* Context, const UAuracronLivingCanyonPCGSettings* Settings,
                                const FAuracronLivingCanyonConfig& Config) const;
    
    /** Apply bioluminescent effects */
    void ApplyBioluminescentEffects(FPCGContext* Context, const UAuracronLivingCanyonPCGSettings* Settings,
                                   const FAuracronLivingCanyonConfig& Config) const;
    
    /** Validate canyon placement */
    bool ValidateCanyonPlacement(const FVector& Location, const UAuracronLivingCanyonPCGSettings* Settings) const;
    
    /** Calculate canyon segment positions */
    TArray<FVector> CalculateCanyonSegments(const FAuracronLivingCanyonConfig& Config, int32 SegmentCount) const;
    
    /** Generate performance-optimized canyon instances */
    void GenerateOptimizedCanyonInstances(FPCGContext* Context, const UAuracronLivingCanyonPCGSettings* Settings,
                                         const TArray<FVector>& SegmentPositions, FPCGDataCollection& OutputData) const;
    
    /** Create breathing timeline component */
    void CreateBreathingTimeline(const FAuracronLivingCanyonBreathingConfig& BreathingConfig) const;
    
    /** Calculate breathing animation value */
    float CalculateBreathingValue(float Time, const FAuracronLivingCanyonBreathingConfig& BreathingConfig) const;
};