// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Lighting Integration Header
// Bridge 3.10: World Partition - Lighting Integration

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Lighting includes for UE5.6
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/SkyLight.h"
// Lumen includes (replacement for deprecated Lightmass)
#include "Lumen/LumenSceneData.h"
#include "Lumen/LumenRadianceCache.h"
// #include "Engine/Classes/Lightmass/VolumetricLightmapDensityVolume.h" // Removed - not available in UE 5.6

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionLighting.generated.h"

// Forward declarations
class UAuracronWorldPartitionLightingManager;
class ULightComponent;
class ALight;

// =============================================================================
// LIGHTING TYPES AND ENUMS
// =============================================================================

// Lighting streaming states
UENUM(BlueprintType)
enum class EAuracronLightingStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Active                  UMETA(DisplayName = "Active"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Lighting LOD levels
UENUM(BlueprintType)
enum class EAuracronLightingLODLevel : uint8
{
    LOD0                    UMETA(DisplayName = "LOD 0 (Highest)"),
    LOD1                    UMETA(DisplayName = "LOD 1"),
    LOD2                    UMETA(DisplayName = "LOD 2"),
    LOD3                    UMETA(DisplayName = "LOD 3"),
    LOD4                    UMETA(DisplayName = "LOD 4 (Lowest)")
};

// Light types
UENUM(BlueprintType)
enum class EAuracronLightType : uint8
{
    Directional             UMETA(DisplayName = "Directional"),
    Point                   UMETA(DisplayName = "Point"),
    Spot                    UMETA(DisplayName = "Spot"),
    Sky                     UMETA(DisplayName = "Sky"),
    Area                    UMETA(DisplayName = "Area"),
    Volumetric              UMETA(DisplayName = "Volumetric")
};

// Light mobility
UENUM(BlueprintType)
enum class EAuracronLightMobility : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Stationary              UMETA(DisplayName = "Stationary"),
    Movable                 UMETA(DisplayName = "Movable")
};

// Shadow quality
UENUM(BlueprintType)
enum class EAuracronShadowQuality : uint8
{
    Low                     UMETA(DisplayName = "Low"),
    Medium                  UMETA(DisplayName = "Medium"),
    High                    UMETA(DisplayName = "High"),
    Epic                    UMETA(DisplayName = "Epic")
};

// =============================================================================
// LIGHTING CONFIGURATION
// =============================================================================

/**
 * Lighting Configuration
 * Configuration settings for lighting streaming in world partition
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLightingConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    bool bEnableLightingStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    bool bEnableLightingLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    bool bEnableLumenIntegration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    bool bEnableLightmapStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    bool bEnableShadowStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float LightingStreamingDistance = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float LightingUnloadingDistance = 30000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 MaxConcurrentLightingOperations = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    int32 DefaultLightmapResolution = 512;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float BaseLODDistance = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LODDistanceMultiplier = 2.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevel = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronShadowQuality DefaultShadowQuality = EAuracronShadowQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float GlobalLightIntensityMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float ShadowDistanceScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxLightingMemoryUsageMB = 512.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLightingCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLightingCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lumen")
    bool bEnableLumenGlobalIllumination = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lumen")
    bool bEnableLumenReflections = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lumen")
    float LumenSceneViewDistance = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lightmap")
    int32 LightmapResolution = 256;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lightmap")
    bool bCompressLightmaps = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableLightingDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogLightingOperations = false;

    FAuracronLightingConfiguration()
    {
        bEnableLightingStreaming = true;
        bEnableLightingLOD = true;
        bEnableLumenIntegration = true;
        bEnableLightmapStreaming = true;
        bEnableShadowStreaming = true;
        LightingStreamingDistance = 20000.0f;
        LightingUnloadingDistance = 30000.0f;
        MaxConcurrentLightingOperations = 8;
        BaseLODDistance = 2000.0f;
        LODDistanceMultiplier = 2.5f;
        MaxLODLevel = 4;
        DefaultShadowQuality = EAuracronShadowQuality::High;
        GlobalLightIntensityMultiplier = 1.0f;
        ShadowDistanceScale = 1.0f;
        MaxLightingMemoryUsageMB = 512.0f;
        bEnableLightingCaching = true;
        bEnableLightingCulling = true;
        bEnableLumenGlobalIllumination = true;
        bEnableLumenReflections = true;
        LumenSceneViewDistance = 20000.0f;
        LightmapResolution = 256;
        bCompressLightmaps = true;
        bEnableLightingDebug = false;
        bLogLightingOperations = false;
    }
};

// =============================================================================
// LIGHTING DESCRIPTOR
// =============================================================================

/**
 * Lighting Descriptor
 * Descriptor for lighting sources and their properties
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLightingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FString LightingId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FString LightingName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FString SourceActorId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    EAuracronLightType LightType = EAuracronLightType::Point;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    EAuracronLightMobility LightMobility = EAuracronLightMobility::Movable;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    EAuracronLightingStreamingState StreamingState = EAuracronLightingStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    EAuracronLightingLODLevel CurrentLODLevel = EAuracronLightingLODLevel::LOD0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FLinearColor LightColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    float Intensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    float AttenuationRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    float Temperature = 6500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    bool bCastShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    bool bCastStaticShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    bool bCastDynamicShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    bool bAffectGlobalIllumination = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    bool bIsEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting Descriptor")
    FDateTime LastAccessTime;

    FAuracronLightingDescriptor()
    {
        LightType = EAuracronLightType::Point;
        LightMobility = EAuracronLightMobility::Movable;
        StreamingState = EAuracronLightingStreamingState::Unloaded;
        CurrentLODLevel = EAuracronLightingLODLevel::LOD0;
        Location = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        LightColor = FLinearColor::White;
        Intensity = 1.0f;
        AttenuationRadius = 1000.0f;
        Temperature = 6500.0f;
        bCastShadows = true;
        bCastStaticShadows = true;
        bCastDynamicShadows = true;
        bAffectGlobalIllumination = true;
        MemoryUsageMB = 0.0f;
        bIsEnabled = true;
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
    }
};

// =============================================================================
// LIGHTING STATISTICS
// =============================================================================

/**
 * Lighting Statistics
 * Performance and usage statistics for lighting system
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronLightingStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalLightSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedLightSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 ActiveLightSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingLightSources = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LODTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float LightingEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LightmapOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 ShadowOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronLightingStatistics()
    {
        TotalLightSources = 0;
        LoadedLightSources = 0;
        ActiveLightSources = 0;
        StreamingLightSources = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        LODTransitions = 0;
        FailedOperations = 0;
        LightingEfficiency = 0.0f;
        LightmapOperations = 0;
        ShadowOperations = 0;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION LIGHTING MANAGER
// =============================================================================

/**
 * World Partition Lighting Manager
 * Central manager for lighting streaming in world partition
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionLightingManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    static UAuracronWorldPartitionLightingManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void Initialize(const FAuracronLightingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void Tick(float DeltaTime);

    // Light source creation and management
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    FString CreateLightSource(const FString& SourceActorId, EAuracronLightType LightType, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool RemoveLightSource(const FString& LightingId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    FAuracronLightingDescriptor GetLightingDescriptor(const FString& LightingId) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FAuracronLightingDescriptor> GetAllLightSources() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FString> GetLightingIds() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool DoesLightSourceExist(const FString& LightingId) const;

    // Lighting streaming
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool LoadLightSource(const FString& LightingId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool UnloadLightSource(const FString& LightingId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    EAuracronLightingStreamingState GetLightingStreamingState(const FString& LightingId) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FString> GetLoadedLightSources() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FString> GetStreamingLightSources() const;

    // Light activation
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool ActivateLightSource(const FString& LightingId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool DeactivateLightSource(const FString& LightingId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FString> GetActiveLightSources() const;

    // Light properties
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightIntensity(const FString& LightingId, float Intensity);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightColor(const FString& LightingId, const FLinearColor& Color);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightLocation(const FString& LightingId, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightRotation(const FString& LightingId, const FRotator& Rotation);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightTemperature(const FString& LightingId, float Temperature);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightAttenuationRadius(const FString& LightingId, float Radius);

    // Lighting LOD
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetLightingLOD(const FString& LightingId, EAuracronLightingLODLevel LODLevel);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    EAuracronLightingLODLevel GetLightingLOD(const FString& LightingId) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void UpdateDistanceBasedLODs(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    EAuracronLightingLODLevel CalculateLODForDistance(float Distance) const;

    // Lighting queries
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FAuracronLightingDescriptor> GetLightSourcesInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FAuracronLightingDescriptor> GetLightSourcesByType(EAuracronLightType LightType) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FAuracronLightingDescriptor> GetInfluencingLightSources(const FVector& Location) const;

    // Cell integration
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    TArray<FString> GetLightSourcesInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    FString GetLightSourceCell(const FString& LightingId) const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool MoveLightSourceToCell(const FString& LightingId, const FString& CellId);

    // Lightmap management
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool BuildLightmapsForCell(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool LoadLightmapsForCell(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool UnloadLightmapsForCell(const FString& CellId);

    // Shadow management
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool EnableShadowsForLight(const FString& LightingId, bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool SetShadowQuality(const FString& LightingId, EAuracronShadowQuality Quality);

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void SetConfiguration(const FAuracronLightingConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    FAuracronLightingConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    FAuracronLightingStatistics GetLightingStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    int32 GetTotalLightSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    int32 GetLoadedLightSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    int32 GetActiveLightSourceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void EnableLightingDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    bool IsLightingDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void LogLightingState() const;

    UFUNCTION(BlueprintCallable, Category = "Lighting Manager")
    void DrawDebugLightingInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLightingLoaded, FString, LightingId, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLightingUnloaded, FString, LightingId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLightingActivated, FString, LightingId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLightingDeactivated, FString, LightingId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLightingLODChanged, FString, LightingId, EAuracronLightingLODLevel, NewLOD);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLightingLoaded OnLightingLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLightingUnloaded OnLightingUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLightingActivated OnLightingActivated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLightingDeactivated OnLightingDeactivated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLightingLODChanged OnLightingLODChanged;

private:
    static UAuracronWorldPartitionLightingManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronLightingConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Lighting data
    TMap<FString, FAuracronLightingDescriptor> LightingDescriptors;
    TMap<FString, TWeakObjectPtr<ULightComponent>> LightComponents;
    TMap<FString, FString> LightingToCellMap; // LightingId -> CellId
    TMap<FString, TSet<FString>> CellToLightingMap; // CellId -> LightingIds

    // Statistics
    FAuracronLightingStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection LightingLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateLightingId(const FString& SourceActorId) const;
    bool ValidateLightingId(const FString& LightingId) const;
    void OnLightingLoadedInternal(const FString& LightingId, bool bSuccess);
    void OnLightingUnloadedInternal(const FString& LightingId);
    void ValidateConfiguration();
    void UpdateLightingCellMapping(const FString& LightingId);
    bool CreateLightComponent(const FString& LightingId, EAuracronLightType LightType);
    bool IsLightInfluencing(const FAuracronLightingDescriptor& Light, const FVector& Location) const;
};
