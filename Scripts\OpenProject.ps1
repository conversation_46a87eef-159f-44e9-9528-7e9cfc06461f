# Script para abrir o projeto Auracron no Unreal Engine 5.6
# Autor: Auracron Development Team
# Data: $(Get-Date -Format 'yyyy-MM-dd')

# Configurações do projeto
$ProjectPath = "C:\Aura\projeto\Auracron\Auracron.uproject"
$UnrealEnginePath = "C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"

# Verificar se o arquivo do projeto existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Arquivo do projeto não encontrado: $ProjectPath"
    exit 1
}

# Verificar se o Unreal Engine está instalado
if (-not (Test-Path $UnrealEnginePath)) {
    Write-Error "Unreal Engine não encontrado: $UnrealEnginePath"
    exit 1
}

# Exibir informações
Write-Host "=== Abrindo Projeto Auracron ===" -ForegroundColor Green
Write-Host "Projeto: $ProjectPath" -ForegroundColor Yellow
Write-Host "Engine: $UnrealEnginePath" -ForegroundColor Yellow
Write-Host "" 

# Abrir o projeto
try {
    Write-Host "Iniciando Unreal Engine..." -ForegroundColor Cyan
    Start-Process -FilePath $UnrealEnginePath -ArgumentList "`"$ProjectPath`"" -NoNewWindow
    Write-Host "Projeto Auracron aberto com sucesso!" -ForegroundColor Green
}
catch {
    Write-Error "Erro ao abrir o projeto: $($_.Exception.Message)"
    exit 1
}

# Aguardar um momento para o engine inicializar
Write-Host "Aguardando inicialização do Unreal Engine..." -ForegroundColor Cyan
Start-Sleep -Seconds 3

Write-Host "Script concluído. O Unreal Engine deve estar carregando o projeto Auracron." -ForegroundColor Green