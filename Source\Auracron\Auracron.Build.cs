// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.IO;

public class Auracron : ModuleRules
{
	public Auracron(ReadOnlyTargetRules Target) : base(Target)
	{
		// Configuração de PCH para melhor compatibilidade
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
		bUseUnity = false; // Desabilita Unity Build para evitar conflitos
		
		// Configuração de caminhos para resolver problemas de inclusão
		PublicIncludePaths.AddRange(new string[] {
			// Adiciona o caminho raiz do módulo para que os includes funcionem corretamente
			Path.Combine(ModuleDirectory)
		});
		
		PrivateIncludePaths.AddRange(new string[] {
			// Adiciona os caminhos para os diretórios Public e Private
			Path.Combine(ModuleDirectory, "Public"),
			Path.Combine(ModuleDirectory, "Private")
		});
	
		// Dependências públicas - módulos essenciais
		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"GameplayTags",
			"UMG",
			"Slate",
			"SlateCore",
			"ApplicationCore" // Adiciona suporte adicional para input
		});

		PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "OnlineSubsystem",
                "OnlineSubsystemUtils",
                "GameplayAbilities",
                "GameplayTasks",
                "EnhancedInput" // Movido para private para resolver problemas com .generated.h
            }
        );
        
        // Configurações adicionais para resolver problemas de compilação
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor"
            });
        }

        // Dependências dinâmicas para evitar dependências circulares
        // Temporariamente removidas para compilação inicial
        // DynamicallyLoadedModuleNames.AddRange(
        //     new string[]
        //     {
        //         "AuracronMasterOrchestrator",
        //         "AuracronDynamicRealmBridge",
        //         "AuracronHarmonyEngineBridge",
        //         "AuracronSigilosBridge",
        //         "AuracronVerticalTransitionsBridge"
        //     }
        // );

		// Uncomment if you are using Slate UI
		// PrivateDependencyModuleNames.AddRange(new string[] { "Slate", "SlateCore" });
		
		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
	}
}

