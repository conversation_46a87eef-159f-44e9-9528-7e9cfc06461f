@echo off
REM Script para abrir o projeto Auracron no Unreal Engine 5.6
REM Autor: Auracron Development Team

setlocal

REM Configurações do projeto
set "PROJECT_PATH=C:\Aura\projeto\Auracron\Auracron.uproject"
set "UNREAL_ENGINE_PATH=C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"

echo ===================================
echo    Abrindo Projeto Auracron
echo ===================================
echo.
echo Projeto: %PROJECT_PATH%
echo Engine: %UNREAL_ENGINE_PATH%
echo.

REM Verificar se o arquivo do projeto existe
if not exist "%PROJECT_PATH%" (
    echo ERRO: Arquivo do projeto nao encontrado!
    echo Caminho: %PROJECT_PATH%
    pause
    exit /b 1
)

REM Verificar se o Unreal Engine está instalado
if not exist "%UNREAL_ENGINE_PATH%" (
    echo ERRO: Unreal Engine nao encontrado!
    echo Caminho: %UNREAL_ENGINE_PATH%
    pause
    exit /b 1
)

REM Abrir o projeto
echo Iniciando Unreal Engine...
start "" "%UNREAL_ENGINE_PATH%" "%PROJECT_PATH%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Projeto Auracron aberto com sucesso!
    echo O Unreal Engine deve estar carregando...
) else (
    echo.
    echo ERRO: Falha ao abrir o projeto!
    pause
    exit /b 1
)

echo.
echo Script concluido. Pressione qualquer tecla para fechar.
pause >nul

endlocal