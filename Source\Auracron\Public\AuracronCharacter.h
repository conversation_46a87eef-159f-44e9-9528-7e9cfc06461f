#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "GameplayTagContainer.h"

// IMPORTANTE: O include do .generated.h DEVE ser o último include
#include "AuracronCharacter.generated.h"

// Forward declarations
class UInputMappingContext;
class UInputAction;
class UCameraComponent;
class USpringArmComponent;
class UStaticMeshComponent;
UCLASS(Blueprintable, BlueprintType)
class AURACRON_API AAuracronCharacter : public ACharacter
{
    GENERATED_BODY()

public:
    AAuracron<PERSON>haracter();

protected:
    virtual void BeginPlay() override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

    // === Input Actions ===
    
    /** Move Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> MoveAction;

    /** Look Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> LookAction;

    /** Jump Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> JumpAction;

    /** Sprint Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> SprintAction;

    /** Primary Attack Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> PrimaryAttackAction;

    /** Secondary Attack Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> SecondaryAttackAction;

    /** Ability 1 Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> Ability1Action;

    /** Ability 2 Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> Ability2Action;

    /** Ultimate Ability Input Action */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputAction> UltimateAction;

    // === Input Mapping Context ===
    
    /** Input Mapping Context */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
    TObjectPtr<UInputMappingContext> DefaultMappingContext;

    // === Components ===
    
    /** Camera boom positioning the camera behind the character */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera")
    TObjectPtr<USpringArmComponent> CameraBoom;

    /** Follow camera */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera")
    TObjectPtr<UCameraComponent> FollowCamera;

public:
    // === Character Stats ===

    /** Current Health */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float Health = 100.0f;

    /** Maximum Health */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MaxHealth = 100.0f;

    /** Current Mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float Mana = 100.0f;

    /** Maximum Mana */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MaxMana = 100.0f;

protected:

    /** Movement Speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float MovementSpeed = 600.0f;

    /** Sprint Speed Multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
    float SprintSpeedMultiplier = 1.5f;

    // === Character State ===
    
    /** Is character sprinting */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsSprinting = false;

    /** Is character attacking */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsAttacking = false;

    /** Current Champion ID */
    UPROPERTY(BlueprintReadWrite, Category = "Champion")
    int32 ChampionID = 0;

    /** Equipped Sigils */
    UPROPERTY(BlueprintReadWrite, Category = "Sigils")
    TArray<FGameplayTag> EquippedSigils;

public:
    // === Input Functions ===
    
    /** Called for movement input */
    void Move(const FInputActionValue& Value);

    /** Called for looking input */
    void Look(const FInputActionValue& Value);

    /** Called when jump action is triggered */
    void StartJump();

    /** Called when jump action is released */
    void StopJump();

    /** Called when sprint action is triggered */
    void StartSprint();

    /** Called when sprint action is released */
    void StopSprint();

    /** Called for primary attack */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void PrimaryAttack();

    /** Called for secondary attack */
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void SecondaryAttack();

    /** Called for ability 1 */
    UFUNCTION(BlueprintCallable, Category = "Abilities")
    void UseAbility1();

    /** Called for ability 2 */
    UFUNCTION(BlueprintCallable, Category = "Abilities")
    void UseAbility2();

    /** Called for ultimate ability */
    UFUNCTION(BlueprintCallable, Category = "Abilities")
    void UseUltimate();

    // === Getters ===
    
    /** Get current health percentage */
    UFUNCTION(BlueprintPure, Category = "Stats")
    float GetHealthPercentage() const { return Health / MaxHealth; }

    /** Get current mana percentage */
    UFUNCTION(BlueprintPure, Category = "Stats")
    float GetManaPercentage() const { return Mana / MaxMana; }

    /** Check if character is alive */
    UFUNCTION(BlueprintPure, Category = "Stats")
    bool IsAlive() const { return Health > 0.0f; }

    /** Get camera boom component */
    UFUNCTION(BlueprintPure, Category = "Camera")
    USpringArmComponent* GetCameraBoom() const { return CameraBoom; }

    /** Get follow camera component */
    UFUNCTION(BlueprintPure, Category = "Camera")
    UCameraComponent* GetFollowCamera() const { return FollowCamera; }

protected:
    /** Update movement speed based on sprint state */
    void UpdateMovementSpeed();

    /** Handle taking damage */
    UFUNCTION(BlueprintNativeEvent, Category = "Combat")
    void OnTakeDamage(float DamageAmount);

    /** Handle death */
    UFUNCTION(BlueprintNativeEvent, Category = "Combat")
    void OnDeath();

    /** Handle ability cooldown */
    UFUNCTION(BlueprintImplementableEvent, Category = "Abilities")
    void OnAbilityCooldown(int32 AbilityIndex, float CooldownTime);
};
