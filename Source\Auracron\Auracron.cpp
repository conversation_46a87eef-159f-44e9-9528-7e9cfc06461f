// Copyright Epic Games, Inc. All Rights Reserved.

#include "Auracron.h"
#include "Modules/ModuleManager.h"

DEFINE_LOG_CATEGORY(LogAuracron);

void FAuracronModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
}

void FAuracronModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module. For modules that support dynamic reloading,
	// we call this function before unloading the module.
}

IMPLEMENT_PRIMARY_GAME_MODULE(FAuracronModule, Auracron, "Auracron");
