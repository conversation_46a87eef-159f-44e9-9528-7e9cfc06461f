// Implementações auxiliares para o sistema de status effects
// Este arquivo contém as funções de apoio para o AuracronCombatBridge

#include "AuracronCombatBridge.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"

// Declaração do log category
DEFINE_LOG_CATEGORY_STATIC(LogAuracronCombat, Log, All);

// ========== FUNÇÕES AUXILIARES DO SISTEMA DE STATUS EFFECTS ==========

bool UAuracronCombatBridge::HasStatusEffect(const FString& EffectName) const
{
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.EffectName == EffectName)
        {
            return true;
        }
    }
    return false;
}

bool UAuracronCombatBridge::HasStatusEffectOfType(EAuracronStatusEffectType StatusType) const
{
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.StatusType == StatusType)
        {
            return true;
        }
    }
    return false;
}

FAuracronActiveStatusEffect UAuracronCombatBridge::GetStatusEffect(const FString& EffectName) const
{
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.EffectName == EffectName)
        {
            return ActiveEffect;
        }
    }
    return FAuracronActiveStatusEffect(); // Retorna efeito vazio se não encontrado
}

TArray<FAuracronActiveStatusEffect> UAuracronCombatBridge::GetAllActiveStatusEffects() const
{
    return ActiveStatusEffects;
}

TArray<FAuracronActiveStatusEffect> UAuracronCombatBridge::GetStatusEffectsByType(EAuracronStatusEffectType StatusType) const
{
    TArray<FAuracronActiveStatusEffect> EffectsOfType;
    
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.StatusType == StatusType)
        {
            EffectsOfType.Add(ActiveEffect);
        }
    }
    
    return EffectsOfType;
}

TArray<FAuracronActiveStatusEffect> UAuracronCombatBridge::GetStatusEffectsByCategory(EAuracronStatusCategory Category) const
{
    TArray<FAuracronActiveStatusEffect> EffectsOfCategory;
    
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.Category == Category)
        {
            EffectsOfCategory.Add(ActiveEffect);
        }
    }
    
    return EffectsOfCategory;
}

bool UAuracronCombatBridge::SetStatusEffectPaused(const FString& EffectID, bool bPaused)
{
    FAuracronActiveStatusEffect* FoundEffect = FindActiveStatusEffectByID(EffectID);
    if (FoundEffect)
    {
        FoundEffect->bIsPaused = bPaused;
        UE_LOG(LogAuracronCombat, Log, TEXT("Status effect %s %s"), 
            *FoundEffect->Effect.EffectName, bPaused ? TEXT("paused") : TEXT("unpaused"));
        return true;
    }
    return false;
}

bool UAuracronCombatBridge::ModifyStatusEffectDuration(const FString& EffectID, float NewDuration)
{
    FAuracronActiveStatusEffect* FoundEffect = FindActiveStatusEffectByID(EffectID);
    if (FoundEffect)
    {
        FoundEffect->RemainingDuration = NewDuration;
        UE_LOG(LogAuracronCombat, Log, TEXT("Status effect %s duration modified to %.2f"), 
            *FoundEffect->Effect.EffectName, NewDuration);
        return true;
    }
    return false;
}

bool UAuracronCombatBridge::AddStatusEffectStacks(const FString& EffectName, int32 StacksToAdd)
{
    FAuracronActiveStatusEffect* FoundEffect = FindActiveStatusEffect(EffectName);
    if (FoundEffect && FoundEffect->Effect.bCanStack)
    {
        int32 NewStacks = FMath::Min(FoundEffect->CurrentStacks + StacksToAdd, FoundEffect->Effect.MaxStacks);
        int32 ActualStacksAdded = NewStacks - FoundEffect->CurrentStacks;
        FoundEffect->CurrentStacks = NewStacks;
        
        UE_LOG(LogAuracronCombat, Log, TEXT("Added %d stacks to %s (Total: %d)"), 
            ActualStacksAdded, *EffectName, NewStacks);
        return ActualStacksAdded > 0;
    }
    return false;
}

bool UAuracronCombatBridge::RemoveStatusEffectStacks(const FString& EffectName, int32 StacksToRemove)
{
    FAuracronActiveStatusEffect* FoundEffect = FindActiveStatusEffect(EffectName);
    if (FoundEffect)
    {
        int32 NewStacks = FMath::Max(FoundEffect->CurrentStacks - StacksToRemove, 0);
        int32 ActualStacksRemoved = FoundEffect->CurrentStacks - NewStacks;
        FoundEffect->CurrentStacks = NewStacks;
        
        // Se chegou a 0 stacks, remover o efeito
        if (NewStacks <= 0)
        {
            RemoveStatusEffectsByName(EffectName);
            UE_LOG(LogAuracronCombat, Log, TEXT("Removed %s due to 0 stacks"), *EffectName);
        }
        else
        {
            UE_LOG(LogAuracronCombat, Log, TEXT("Removed %d stacks from %s (Remaining: %d)"), 
                ActualStacksRemoved, *EffectName, NewStacks);
        }
        
        return ActualStacksRemoved > 0;
    }
    return false;
}

FString UAuracronCombatBridge::GetStatusEffectsSummary() const
{
    if (ActiveStatusEffects.Num() == 0)
    {
        return TEXT("No active status effects");
    }
    
    FString Summary = FString::Printf(TEXT("Active Status Effects (%d):\n"), ActiveStatusEffects.Num());
    
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        FString StackInfo = ActiveEffect.Effect.bCanStack && ActiveEffect.CurrentStacks > 1 ? 
            FString::Printf(TEXT(" x%d"), ActiveEffect.CurrentStacks) : TEXT("");
            
        FString DurationInfo = ActiveEffect.Effect.Duration > 0.0f ? 
            FString::Printf(TEXT(" (%.1fs)"), ActiveEffect.RemainingDuration) : TEXT(" (Permanent)");
            
        Summary += FString::Printf(TEXT("- %s%s%s\n"), 
            *ActiveEffect.Effect.EffectName, *StackInfo, *DurationInfo);
    }
    
    return Summary;
}

// ========== FUNÇÕES PRIVADAS AUXILIARES ==========

FAuracronActiveStatusEffect* UAuracronCombatBridge::FindActiveStatusEffect(const FString& EffectName)
{
    for (FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.EffectName == EffectName)
        {
            return &ActiveEffect;
        }
    }
    return nullptr;
}

FAuracronActiveStatusEffect* UAuracronCombatBridge::FindActiveStatusEffectByID(const FString& EffectID)
{
    for (FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.EffectID == EffectID)
        {
            return &ActiveEffect;
        }
    }
    return nullptr;
}

bool UAuracronCombatBridge::CanApplyStatusEffect(const FAuracronStatusEffect& StatusEffect) const
{
    // Verificar imunidades
    if (IsImmuneToStatusEffect(StatusEffect))
    {
        UE_LOG(LogAuracronCombat, Log, TEXT("Cannot apply %s: target is immune"), *StatusEffect.EffectName);
        return false;
    }
    
    // Verificar se o nome do efeito é válido
    if (StatusEffect.EffectName.IsEmpty())
    {
        UE_LOG(LogAuracronCombat, Warning, TEXT("Cannot apply status effect: empty name"));
        return false;
    }
    
    // Verificar se a duração é válida
    if (StatusEffect.Duration == 0.0f)
    {
        UE_LOG(LogAuracronCombat, Warning, TEXT("Cannot apply status effect %s: invalid duration"), *StatusEffect.EffectName);
        return false;
    }
    
    return true;
}

float UAuracronCombatBridge::ApplyStatusEffectModifiers(float BaseValue, const FString& AttributeName) const
{
    float ModifiedValue = BaseValue;
    
    for (const FAuracronActiveStatusEffect& ActiveEffect : ActiveStatusEffects)
    {
        if (ActiveEffect.Effect.AttributeModifiers.Contains(AttributeName))
        {
            float Modifier = ActiveEffect.Effect.AttributeModifiers[AttributeName];
            float StackMultiplier = ActiveEffect.CurrentStacks;
            
            // Aplicar modificador baseado no tipo de efeito
            switch (ActiveEffect.Effect.StatusType)
            {
                case EAuracronStatusEffectType::DamageBoost:
                case EAuracronStatusEffectType::SpeedBoost:
                case EAuracronStatusEffectType::DefenseBoost:
                    // Multiplicativo para buffs
                    ModifiedValue *= (1.0f + (Modifier * StackMultiplier));
                    break;
                    
                case EAuracronStatusEffectType::Weakness:
                case EAuracronStatusEffectType::Slow:
                case EAuracronStatusEffectType::Vulnerability:
                    // Redutivo para debuffs
                    ModifiedValue *= (1.0f - (Modifier * StackMultiplier));
                    break;
                    
                default:
                    // Aditivo para outros tipos
                    ModifiedValue += (Modifier * StackMultiplier);
                    break;
            }
        }
    }
    
    return ModifiedValue;
}

void UAuracronCombatBridge::InitializeStatusEffectTemplates()
{
    // Limpar templates existentes
    StatusEffectTemplates.Empty();
    
    // ========== BUFFS ==========
    
    // Damage Boost
    FAuracronStatusEffect DamageBoost;
    DamageBoost.EffectName = TEXT("Damage Boost");
    DamageBoost.StatusType = EAuracronStatusEffectType::DamageBoost;
    DamageBoost.Category = EAuracronStatusCategory::Combat;
    DamageBoost.Duration = 30.0f;
    DamageBoost.Magnitude = 1.25f;
    DamageBoost.bCanStack = true;
    DamageBoost.MaxStacks = 3;
    DamageBoost.Priority = 5;
    DamageBoost.AttributeModifiers.Add(TEXT("Damage"), 0.25f);
    DamageBoost.VFXName = TEXT("DamageBoostVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::DamageBoost, DamageBoost);
    
    // Speed Boost
    FAuracronStatusEffect SpeedBoost;
    SpeedBoost.EffectName = TEXT("Speed Boost");
    SpeedBoost.StatusType = EAuracronStatusEffectType::SpeedBoost;
    SpeedBoost.Category = EAuracronStatusCategory::Movement;
    SpeedBoost.Duration = 20.0f;
    SpeedBoost.Magnitude = 1.5f;
    SpeedBoost.bCanStack = false;
    SpeedBoost.Priority = 3;
    SpeedBoost.AttributeModifiers.Add(TEXT("Speed"), 0.5f);
    SpeedBoost.VFXName = TEXT("SpeedBoostVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::SpeedBoost, SpeedBoost);
    
    // Health Regeneration
    FAuracronStatusEffect HealthRegen;
    HealthRegen.EffectName = TEXT("Health Regeneration");
    HealthRegen.StatusType = EAuracronStatusEffectType::HealthRegeneration;
    HealthRegen.Category = EAuracronStatusCategory::Resource;
    HealthRegen.Duration = 60.0f;
    HealthRegen.TickInterval = 2.0f;
    HealthRegen.TickValue = 10.0f;
    HealthRegen.bCanStack = true;
    HealthRegen.MaxStacks = 5;
    HealthRegen.Priority = 4;
    HealthRegen.VFXName = TEXT("HealthRegenVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::HealthRegeneration, HealthRegen);
    
    // ========== DEBUFFS ==========
    
    // Poison
    FAuracronStatusEffect Poison;
    Poison.EffectName = TEXT("Poison");
    Poison.StatusType = EAuracronStatusEffectType::Poison;
    Poison.Category = EAuracronStatusCategory::Environmental;
    Poison.Duration = 15.0f;
    Poison.TickInterval = 1.0f;
    Poison.TickValue = -5.0f;
    Poison.bCanStack = true;
    Poison.MaxStacks = 10;
    Poison.Priority = 6;
    Poison.VFXName = TEXT("PoisonVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::Poison, Poison);
    
    // Burn
    FAuracronStatusEffect Burn;
    Burn.EffectName = TEXT("Burn");
    Burn.StatusType = EAuracronStatusEffectType::Burn;
    Burn.Category = EAuracronStatusCategory::Environmental;
    Burn.Duration = 10.0f;
    Burn.TickInterval = 0.5f;
    Burn.TickValue = -8.0f;
    Burn.bCanStack = true;
    Burn.MaxStacks = 5;
    Burn.Priority = 7;
    Burn.VFXName = TEXT("BurnVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::Burn, Burn);
    
    // Slow
    FAuracronStatusEffect Slow;
    Slow.EffectName = TEXT("Slow");
    Slow.StatusType = EAuracronStatusEffectType::Slow;
    Slow.Category = EAuracronStatusCategory::Movement;
    Slow.Duration = 8.0f;
    Slow.Magnitude = 0.5f;
    Slow.bCanStack = false;
    Slow.Priority = 4;
    Slow.AttributeModifiers.Add(TEXT("Speed"), 0.5f);
    Slow.VFXName = TEXT("SlowVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::Slow, Slow);
    
    // Stun
    FAuracronStatusEffect Stun;
    Stun.EffectName = TEXT("Stun");
    Stun.StatusType = EAuracronStatusEffectType::Stun;
    Stun.Category = EAuracronStatusCategory::Control;
    Stun.Duration = 3.0f;
    Stun.bCanStack = false;
    Stun.bCanBeDispelled = false;
    Stun.Priority = 10;
    Stun.AttributeModifiers.Add(TEXT("Speed"), 1.0f); // Reduz velocidade a 0
    Stun.VFXName = TEXT("StunVFX");
    StatusEffectTemplates.Add(EAuracronStatusEffectType::Stun, Stun);
    
    UE_LOG(LogAuracronCombat, Log, TEXT("Initialized %d status effect templates"), StatusEffectTemplates.Num());
}

void UAuracronCombatBridge::ApplyStatusEffectVFX(const FAuracronActiveStatusEffect& ActiveEffect)
{
    if (ActiveEffect.Effect.VFXName.IsEmpty())
    {
        return;
    }
    
    // Integração com AuracronVFXBridge para aplicar efeitos visuais
    // TODO: Implementar chamada para VFXBridge quando disponível
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Applying VFX: %s for status effect: %s"), 
        *ActiveEffect.Effect.VFXName, *ActiveEffect.Effect.EffectName);
}

void UAuracronCombatBridge::RemoveStatusEffectVFX(const FAuracronActiveStatusEffect& ActiveEffect)
{
    if (ActiveEffect.Effect.VFXName.IsEmpty())
    {
        return;
    }
    
    // Integração com AuracronVFXBridge para remover efeitos visuais
    // TODO: Implementar chamada para VFXBridge quando disponível
    UE_LOG(LogAuracronCombat, VeryVerbose, TEXT("Removing VFX: %s for status effect: %s"), 
        *ActiveEffect.Effect.VFXName, *ActiveEffect.Effect.EffectName);
}

bool UAuracronCombatBridge::IsImmuneToStatusEffect(const FAuracronStatusEffect& StatusEffect) const
{
    // Verificar se tem imunidade geral
    if (HasStatusEffectOfType(EAuracronStatusEffectType::Immunity))
    {
        return true;
    }
    
    // Verificar imunidades específicas baseadas em tags
    for (const FString& Tag : StatusEffect.EffectTags)
    {
        // Lógica de imunidade baseada em tags
        // TODO: Implementar sistema de tags de imunidade mais complexo
        if (Tag.Contains(TEXT("Poison")) && HasStatusEffect(TEXT("Poison Immunity")))
        {
            return true;
        }
        if (Tag.Contains(TEXT("Fire")) && HasStatusEffect(TEXT("Fire Immunity")))
        {
            return true;
        }
    }
    
    return false;
}