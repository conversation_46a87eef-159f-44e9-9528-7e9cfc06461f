// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sigils Subsystem Implementation
// UE 5.6 Compatible Implementation

#include "AuracronSigilosSubsystem.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "GameFramework/Pawn.h"
#include "Logging/LogMacros.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronSigils, Log, All);

UAuracronSigilosSubsystem::UAuracronSigilosSubsystem()
{
    // Initialize subsystem
}

void UAuracronSigilosSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogAuracronSigils, Log, TEXT("UAuracronSigilosSubsystem: Initialized"));
    
    // Initialize player sigil map
    PlayerSigilMap.Empty();
}

void UAuracronSigilosSubsystem::Deinitialize()
{
    // Clear timer
    if (UWorld* World = GetWorld())
    {
        if (FTimerManager* TimerManager = &World->GetTimerManager())
        {
            TimerManager->ClearTimer(SigilTimerHandle);
        }
    }
    
    // Clear all data
    PlayerSigilMap.Empty();
    
    UE_LOG(LogAuracronSigils, Log, TEXT("UAuracronSigilosSubsystem: Deinitialized"));
    
    Super::Deinitialize();
}

bool UAuracronSigilosSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // Only create in game worlds
    if (UWorld* World = Cast<UWorld>(Outer))
    {
        return World->IsGameWorld();
    }
    return false;
}

void UAuracronSigilosSubsystem::OnWorldBeginPlay(UWorld& InWorld)
{
    Super::OnWorldBeginPlay(InWorld);
    
    UE_LOG(LogAuracronSigils, Log, TEXT("UAuracronSigilosSubsystem: World begin play"));
    
    // Start timer for sigil duration management
    if (FTimerManager* TimerManager = &InWorld.GetTimerManager())
    {
        TimerManager->SetTimer(SigilTimerHandle, this, &UAuracronSigilosSubsystem::UpdateSigilDurations, 1.0f, true);
    }
}

bool UAuracronSigilosSubsystem::DoesSupportWorldType(const EWorldType::Type WorldType) const
{
    // Support game and PIE worlds
    return WorldType == EWorldType::Game || WorldType == EWorldType::PIE;
}

TArray<EAuracronSigiloType> UAuracronSigilosSubsystem::GetActiveSigils(APawn* Player) const
{
    if (!IsValid(Player))
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("GetActiveSigils: Invalid player"));
        return TArray<EAuracronSigiloType>();
    }
    
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    if (const FPlayerSigilData* SigilData = PlayerSigilMap.Find(PlayerPtr))
    {
        return SigilData->ActiveSigils;
    }
    
    return TArray<EAuracronSigiloType>();
}

bool UAuracronSigilosSubsystem::ActivateSigil(APawn* Player, EAuracronSigiloType SigilType, float Duration)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("ActivateSigil: Invalid player"));
        return false;
    }
    
    if (SigilType == EAuracronSigiloType::None)
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("ActivateSigil: Invalid sigil type None"));
        return false;
    }
    
    FPlayerSigilData& SigilData = GetOrCreatePlayerSigilData(Player);
    
    // Check if sigil is already active
    if (SigilData.ActiveSigils.Contains(SigilType))
    {
        UE_LOG(LogAuracronSigils, Log, TEXT("ActivateSigil: Sigil %d already active for player %s"), 
               (int32)SigilType, *Player->GetName());
        return false;
    }
    
    // Add sigil to active list
    SigilData.ActiveSigils.Add(SigilType);
    
    // Set timestamp and duration
    if (UWorld* World = GetWorld())
    {
        float CurrentTime = World->GetTimeSeconds();
        SigilData.SigilTimestamps.Add(SigilType, CurrentTime);
        SigilData.SigilDurations.Add(SigilType, Duration);
    }
    
    UE_LOG(LogAuracronSigils, Log, TEXT("ActivateSigil: Activated sigil %d for player %s (Duration: %.2f)"), 
           (int32)SigilType, *Player->GetName(), Duration);
    
    // Broadcast event
    OnSigilActivated.Broadcast(Player, SigilType, Duration);
    
    return true;
}

bool UAuracronSigilosSubsystem::DeactivateSigil(APawn* Player, EAuracronSigiloType SigilType)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("DeactivateSigil: Invalid player"));
        return false;
    }
    
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    FPlayerSigilData* SigilData = PlayerSigilMap.Find(PlayerPtr);
    
    if (!SigilData)
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("DeactivateSigil: No sigil data found for player %s"), *Player->GetName());
        return false;
    }
    
    // Remove sigil from active list
    int32 RemovedCount = SigilData->ActiveSigils.Remove(SigilType);
    
    if (RemovedCount > 0)
    {
        // Remove timestamp and duration data
        SigilData->SigilTimestamps.Remove(SigilType);
        SigilData->SigilDurations.Remove(SigilType);
        
        UE_LOG(LogAuracronSigils, Log, TEXT("DeactivateSigil: Deactivated sigil %d for player %s"), 
               (int32)SigilType, *Player->GetName());
        
        // Broadcast event
        OnSigilDeactivated.Broadcast(Player, SigilType);
        
        return true;
    }
    
    UE_LOG(LogAuracronSigils, Warning, TEXT("DeactivateSigil: Sigil %d was not active for player %s"), 
           (int32)SigilType, *Player->GetName());
    
    return false;
}

bool UAuracronSigilosSubsystem::HasActiveSigil(APawn* Player, EAuracronSigiloType SigilType) const
{
    if (!IsValid(Player))
    {
        return false;
    }
    
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    if (const FPlayerSigilData* SigilData = PlayerSigilMap.Find(PlayerPtr))
    {
        return SigilData->ActiveSigils.Contains(SigilType);
    }
    
    return false;
}

int32 UAuracronSigilosSubsystem::GetActiveSigilCount(APawn* Player) const
{
    if (!IsValid(Player))
    {
        return 0;
    }
    
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    if (const FPlayerSigilData* SigilData = PlayerSigilMap.Find(PlayerPtr))
    {
        return SigilData->ActiveSigils.Num();
    }
    
    return 0;
}

void UAuracronSigilosSubsystem::ClearAllSigils(APawn* Player)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogAuracronSigils, Warning, TEXT("ClearAllSigils: Invalid player"));
        return;
    }
    
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    FPlayerSigilData* SigilData = PlayerSigilMap.Find(PlayerPtr);
    
    if (!SigilData)
    {
        return;
    }
    
    // Broadcast deactivation events for all active sigils
    for (EAuracronSigiloType SigilType : SigilData->ActiveSigils)
    {
        OnSigilDeactivated.Broadcast(Player, SigilType);
    }
    
    // Clear all sigil data
    SigilData->ActiveSigils.Empty();
    SigilData->SigilTimestamps.Empty();
    SigilData->SigilDurations.Empty();
    
    UE_LOG(LogAuracronSigils, Log, TEXT("ClearAllSigils: Cleared all sigils for player %s"), *Player->GetName());
}

FPlayerSigilData& UAuracronSigilosSubsystem::GetOrCreatePlayerSigilData(APawn* Player)
{
    TWeakObjectPtr<APawn> PlayerPtr(Player);
    
    if (!PlayerSigilMap.Contains(PlayerPtr))
    {
        PlayerSigilMap.Add(PlayerPtr, FPlayerSigilData());
        UE_LOG(LogAuracronSigils, Log, TEXT("GetOrCreatePlayerSigilData: Created new sigil data for player %s"), *Player->GetName());
    }
    
    return PlayerSigilMap[PlayerPtr];
}

void UAuracronSigilosSubsystem::UpdateSigilDurations()
{
    if (!GetWorld())
    {
        return;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    TArray<TWeakObjectPtr<APawn>> PlayersToCleanup;
    
    // Check all players for expired sigils
    for (auto& PlayerSigilPair : PlayerSigilMap)
    {
        TWeakObjectPtr<APawn> PlayerPtr = PlayerSigilPair.Key;
        FPlayerSigilData& SigilData = PlayerSigilPair.Value;
        
        // Check if player is still valid
        if (!PlayerPtr.IsValid())
        {
            PlayersToCleanup.Add(PlayerPtr);
            continue;
        }
        
        APawn* Player = PlayerPtr.Get();
        TArray<EAuracronSigiloType> SigilsToRemove;
        
        // Check each active sigil for expiration
        for (EAuracronSigiloType SigilType : SigilData.ActiveSigils)
        {
            float* Duration = SigilData.SigilDurations.Find(SigilType);
            float* Timestamp = SigilData.SigilTimestamps.Find(SigilType);
            
            if (Duration && Timestamp && *Duration > 0.0f)
            {
                float ElapsedTime = CurrentTime - *Timestamp;
                if (ElapsedTime >= *Duration)
                {
                    SigilsToRemove.Add(SigilType);
                }
            }
        }
        
        // Remove expired sigils
        for (EAuracronSigiloType SigilType : SigilsToRemove)
        {
            DeactivateSigil(Player, SigilType);
        }
    }
    
    // Clean up invalid players
    for (TWeakObjectPtr<APawn> PlayerPtr : PlayersToCleanup)
    {
        PlayerSigilMap.Remove(PlayerPtr);
    }
}

void UAuracronSigilosSubsystem::CleanupInvalidPlayers()
{
    TArray<TWeakObjectPtr<APawn>> PlayersToRemove;
    
    for (auto& PlayerSigilPair : PlayerSigilMap)
    {
        if (!PlayerSigilPair.Key.IsValid())
        {
            PlayersToRemove.Add(PlayerSigilPair.Key);
        }
    }
    
    for (TWeakObjectPtr<APawn> PlayerPtr : PlayersToRemove)
    {
        PlayerSigilMap.Remove(PlayerPtr);
        UE_LOG(LogAuracronSigils, Log, TEXT("CleanupInvalidPlayers: Removed invalid player reference"));
    }
}