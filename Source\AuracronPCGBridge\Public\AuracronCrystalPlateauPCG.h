// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Crystal Plateau Generator
// Bridge 2.2: PCG Framework - Geological Features

#pragma once

#include "CoreMinimal.h"
#include "AuracronPCGBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "AuracronCrystalPlateauPCG.generated.h"

/**
 * Crystal Plateau Configuration Structure
 * Defines properties for individual crystal plateau generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronCrystalPlateauConfig
{
    GENERATED_BODY()

    /** Plateau base location */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Location")
    FVector BaseLocation = FVector::ZeroVector;

    /** Plateau base radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "500.0", ClampMax = "5000.0"))
    float BaseRadius = 1500.0f;

    /** Plateau height */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Size", meta = (ClampMin = "200.0", ClampMax = "2000.0"))
    float PlateauHeight = 800.0f;

    /** Crystal density on plateau */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crystals", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float CrystalDensity = 1.5f;

    /** Crystal size variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crystals")
    FVector2D CrystalSizeRange = FVector2D(0.5f, 3.0f);

    /** Crystal color variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crystals")
    FLinearColor CrystalColor = FLinearColor::Blue;

    /** Enable magical glow effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bEnableMagicalGlow = true;

    /** Glow intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float GlowIntensity = 2.5f;
};

/**
 * Auracron Crystal Plateau PCG Settings
 * Generates elevated crystal formations with magical properties
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Geological")
class AURACRONPCGBRIDGE_API UAuracronCrystalPlateauPCGSettings : public UAuracronTerrainPCGSettings
{
    GENERATED_BODY()

public:
    UAuracronCrystalPlateauPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Array of crystal plateau configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Plateaus")
    TArray<FAuracronCrystalPlateauConfig> PlateauConfigurations;

    /** Crystal mesh to use for generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UStaticMesh> CrystalMesh;

    /** Crystal material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UMaterialInterface> CrystalMaterial;

    /** Plateau base material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
    TSoftObjectPtr<UMaterialInterface> PlateauMaterial;

    /** Enable performance optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceOptimization = true;

    /** Maximum crystals per plateau */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "10", ClampMax = "1000"))
    int32 MaxCrystalsPerPlateau = 200;

    /** LOD distance for crystals */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "10000.0"))
    float CrystalLODDistance = 5000.0f;

    /** Integration with VFX Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseVFXBridge = true;

    /** Integration with Audio Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseAudioBridge = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Crystal Plateau PCG Element
 * Handles the actual generation of crystal plateaus
 */
class AURACRONPCGBRIDGE_API FAuracronCrystalPlateauPCGElement : public FAuracronTerrainPCGElement
{
public:
    FAuracronCrystalPlateauPCGElement() = default;
    virtual ~FAuracronCrystalPlateauPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    
    /** Generate plateau base terrain */
    void GeneratePlateauBase(FPCGContext* Context, const UAuracronCrystalPlateauPCGSettings* Settings, 
                           const FAuracronCrystalPlateauConfig& Config, FPCGDataCollection& OutputData) const;
    
    /** Generate crystals on plateau */
    void GenerateCrystals(FPCGContext* Context, const UAuracronCrystalPlateauPCGSettings* Settings,
                         const FAuracronCrystalPlateauConfig& Config, FPCGDataCollection& OutputData) const;
    
    /** Apply magical effects */
    void ApplyMagicalEffects(FPCGContext* Context, const UAuracronCrystalPlateauPCGSettings* Settings,
                           const FAuracronCrystalPlateauConfig& Config) const;
    
    /** Validate plateau placement */
    bool ValidatePlateauPlacement(const FVector& Location, const UAuracronCrystalPlateauPCGSettings* Settings) const;
    
    /** Calculate crystal position on plateau */
    FVector CalculateCrystalPosition(const FVector& PlateauCenter, float PlateauRadius, 
                                   float PlateauHeight, int32 CrystalIndex, int32 TotalCrystals) const;
    
    /** Generate performance-optimized crystal instances */
    void GenerateOptimizedCrystalInstances(FPCGContext* Context, const UAuracronCrystalPlateauPCGSettings* Settings,
                                         const TArray<FVector>& CrystalPositions, FPCGDataCollection& OutputData) const;
};