// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge
// Integração e coordenação dos sistemas de combate existentes
// Autor: Auracron Development Team
// Data: 2025-01-21
// Versão: 1.0.0

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayTagContainer.h"
#include "GameFramework/Character.h"
#include "Net/Core/PushModel/PushModel.h"

// Integração com outros bridges existentes
#include "AuracronCombatBridgeAPI.h"
#include "AuracronSigilosBridge/Public/AuracronSigilosBridge.h"
#include "AuracronVerticalTransitionsBridge/Public/AuracronVerticalTransitionsBridge.h"
#include "AuracronRealmsBridge/Public/AuracronRealmsBridge.h"
// #include "AuracronCombatInterface.h" // File does not exist
#include "AuracronCombatPerformanceConfig.h"

#include "AuracronCombatBridge.generated.h"

// Forward Declarations
class UAuracronSigilosBridge;
class UAuracronVerticalTransitionsBridge;
class UAuracronRealmsBridge;
class UAbilitySystemComponent;

/**
 * Enumeração para as camadas de combate vertical
 * Integra com os sistemas existentes de transições verticais
 */
UENUM(BlueprintType)
enum class EAuracronCombatLayer : uint8
{
    Surface     UMETA(DisplayName = "Surface Layer"),
    Sky         UMETA(DisplayName = "Sky Layer"), 
    Underground UMETA(DisplayName = "Underground Layer")
};

/**
 * Enumeração para tipos de dano no sistema adaptativo
 * Permite resistências específicas e balanceamento fino
 */
UENUM(BlueprintType)
enum class EAuracronDamageType : uint8
{
    Physical    UMETA(DisplayName = "Physical Damage"),
    Magical     UMETA(DisplayName = "Magical Damage"),
    Fire        UMETA(DisplayName = "Fire Damage"),
    Ice         UMETA(DisplayName = "Ice Damage"),
    Lightning   UMETA(DisplayName = "Lightning Damage"),
    Poison      UMETA(DisplayName = "Poison Damage"),
    Psychic     UMETA(DisplayName = "Psychic Damage"),
    TrueDamage  UMETA(DisplayName = "True Damage") // Ignora resistências
};

/**
 * Estrutura para histórico de movimento de um ator
 * Wrapper necessário para uso em TMap com UPROPERTY
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronMovementHistory
{
    GENERATED_BODY()

    /** Array de posições históricas */
    UPROPERTY(BlueprintReadOnly, Category = "Movement")
    TArray<FVector> Positions;

    /** Timestamps das posições */
    UPROPERTY(BlueprintReadOnly, Category = "Movement")
    TArray<float> Timestamps;

    FAuracronMovementHistory()
    {
        Positions.Empty();
        Timestamps.Empty();
    }
};

/**
 * Estrutura para modificadores de dano por tipo
 * Permite balanceamento fino entre diferentes tipos de dano
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronDamageTypeModifiers
{
    GENERATED_BODY()

    /** Multiplicador base para este tipo de dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float BaseMultiplier = 1.0f;

    /** Resistência base contra este tipo de dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float BaseResistance = 0.0f;

    /** Taxa de acúmulo de resistência adaptativa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float ResistanceBuildupRate = 0.1f;

    /** Resistência máxima alcançável */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float MaxResistance = 0.5f;

    FAuracronDamageTypeModifiers()
    {
        BaseMultiplier = 1.0f;
        BaseResistance = 0.0f;
        ResistanceBuildupRate = 0.1f;
        MaxResistance = 0.5f;
    }
};

/**
 * Estrutura para dados de combate por camada
 * Coordena com os sistemas de sígilos e realms existentes
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronLayerCombatData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    EAuracronCombatLayer Layer = EAuracronCombatLayer::Surface;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    float DamageMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    float RangeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    float AreaOfEffectMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    bool bCanTargetOtherLayers = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Layer")
    TArray<EAuracronCombatLayer> TargetableLayers;
};

/**
 * Tipos de efeitos de camada
 */
UENUM(BlueprintType)
enum class EAuracronLayerEffectType : uint8
{
    Buff        UMETA(DisplayName = "Buff"),
    Debuff      UMETA(DisplayName = "Debuff"),
    Neutral     UMETA(DisplayName = "Neutral")
};

/** Tipos de status effects mais específicos */
UENUM(BlueprintType)
enum class EAuracronStatusEffectType : uint8
{
    // Buffs
    DamageBoost         UMETA(DisplayName = "Damage Boost"),
    SpeedBoost          UMETA(DisplayName = "Speed Boost"),
    DefenseBoost        UMETA(DisplayName = "Defense Boost"),
    HealthRegeneration  UMETA(DisplayName = "Health Regeneration"),
    ManaRegeneration    UMETA(DisplayName = "Mana Regeneration"),
    CriticalChance      UMETA(DisplayName = "Critical Chance"),
    Invisibility        UMETA(DisplayName = "Invisibility"),
    Shield              UMETA(DisplayName = "Shield"),
    
    // Debuffs
    Poison              UMETA(DisplayName = "Poison"),
    Burn                UMETA(DisplayName = "Burn"),
    Freeze              UMETA(DisplayName = "Freeze"),
    Slow                UMETA(DisplayName = "Slow"),
    Weakness            UMETA(DisplayName = "Weakness"),
    Vulnerability       UMETA(DisplayName = "Vulnerability"),
    Silence             UMETA(DisplayName = "Silence"),
    Stun                UMETA(DisplayName = "Stun"),
    Blind               UMETA(DisplayName = "Blind"),
    
    // Neutros/Especiais
    Mark                UMETA(DisplayName = "Mark"),
    Transform           UMETA(DisplayName = "Transform"),
    Immunity            UMETA(DisplayName = "Immunity"),
    Custom              UMETA(DisplayName = "Custom")
};

/** Categorias de status effects para organização */
UENUM(BlueprintType)
enum class EAuracronStatusCategory : uint8
{
    Combat              UMETA(DisplayName = "Combat"),
    Movement            UMETA(DisplayName = "Movement"),
    Resource            UMETA(DisplayName = "Resource"),
    Control             UMETA(DisplayName = "Control"),
    Environmental       UMETA(DisplayName = "Environmental"),
    Special             UMETA(DisplayName = "Special")
};

/**
 * Contexto para sistema avançado de targeting com IA
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronTargetingContext
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    FString AttackType = "";

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    float AggressionLevel = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    bool bPrioritizeWeakTargets = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    bool bConsiderTacticalValue = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    bool bUsePredictiveTargeting = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    float ThreatWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    float DistanceWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    float HealthWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Context")
    TArray<EAuracronCombatLayer> PreferredLayers;

    FAuracronTargetingContext()
    {
        AttackType = "";
        AggressionLevel = 0.5f;
        bPrioritizeWeakTargets = true;
        bConsiderTacticalValue = true;
        bUsePredictiveTargeting = false;
        ThreatWeight = 1.0f;
        DistanceWeight = 1.0f;
        HealthWeight = 1.0f;
    }
};

/**
 * Estrutura para efeitos de camada de combate
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronLayerEffect
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    FString EffectName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    EAuracronLayerEffectType EffectType = EAuracronLayerEffectType::Neutral;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    EAuracronCombatLayer TargetLayer = EAuracronCombatLayer::Surface;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    float DamageModifier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    float SpeedModifier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    float DefenseModifier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    float Duration = 10.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    bool bStackable = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effect")
    int32 MaxStacks = 1;

    FAuracronLayerEffect()
    {
        EffectName = TEXT("Default Effect");
        EffectType = EAuracronLayerEffectType::Neutral;
        TargetLayer = EAuracronCombatLayer::Surface;
        DamageModifier = 1.0f;
        SpeedModifier = 1.0f;
        DefenseModifier = 1.0f;
        Duration = 10.0f;
        bStackable = false;
        MaxStacks = 1;
    }
};

/**
 * Instância ativa de um efeito de camada
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronActiveLayerEffect
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Active Effect")
    FAuracronLayerEffect Effect;

    UPROPERTY(BlueprintReadOnly, Category = "Active Effect")
    float RemainingDuration = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Active Effect")
    int32 CurrentStacks = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Active Effect")
    float StartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Active Effect")
    TWeakObjectPtr<AActor> Source;

    FAuracronActiveLayerEffect()
    {
        RemainingDuration = 0.0f;
        CurrentStacks = 1;
        StartTime = 0.0f;
    }

    FAuracronActiveLayerEffect(const FAuracronLayerEffect& InEffect, AActor* InSource = nullptr)
        : Effect(InEffect)
        , RemainingDuration(InEffect.Duration)
        , CurrentStacks(1)
        , StartTime(0.0f)
        , Source(InSource)
    {
    }
};

/**
 * Status Effect completo com propriedades avançadas
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronStatusEffect
{
    GENERATED_BODY()

    /** Nome único do status effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    FString EffectName;

    /** Tipo específico do status effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    EAuracronStatusEffectType StatusType;

    /** Categoria do status effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    EAuracronStatusCategory Category;

    /** Duração do efeito (-1 para permanente) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    float Duration;

    /** Intensidade/magnitude do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    float Magnitude;

    /** Intervalo entre aplicações (para efeitos periódicos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    float TickInterval;

    /** Valor aplicado a cada tick (para DoT/HoT) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    float TickValue;

    /** Se o efeito pode ser empilhado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    bool bCanStack;

    /** Número máximo de stacks */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    int32 MaxStacks;

    /** Se o efeito pode ser removido/dispelido */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    bool bCanBeDispelled;

    /** Prioridade do efeito (maior = mais importante) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    int32 Priority;

    /** Tags associadas ao efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    TArray<FString> EffectTags;

    /** Modificadores de atributos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    TMap<FString, float> AttributeModifiers;

    /** Efeitos visuais associados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    FString VFXName;

    /** Som associado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effect")
    FString SFXName;

    FAuracronStatusEffect()
    {
        EffectName = TEXT("");
        StatusType = EAuracronStatusEffectType::Custom;
        Category = EAuracronStatusCategory::Special;
        Duration = 10.0f;
        Magnitude = 1.0f;
        TickInterval = 1.0f;
        TickValue = 0.0f;
        bCanStack = false;
        MaxStacks = 1;
        bCanBeDispelled = true;
        Priority = 0;
        VFXName = TEXT("");
        SFXName = TEXT("");
    }
};

/**
 * Status Effect ativo com informações de runtime
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronActiveStatusEffect
{
    GENERATED_BODY()

    /** Definição do status effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    FAuracronStatusEffect Effect;

    /** Tempo de início */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    float StartTime;

    /** Duração restante */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    float RemainingDuration;

    /** Tempo do último tick */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    float LastTickTime;

    /** Número atual de stacks */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    int32 CurrentStacks;

    /** Fonte do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    TWeakObjectPtr<AActor> Source;

    /** ID único para tracking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    FString EffectID;

    /** Se o efeito está pausado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Status Effect")
    bool bIsPaused;

    FAuracronActiveStatusEffect()
    {
        StartTime = 0.0f;
        RemainingDuration = 0.0f;
        LastTickTime = 0.0f;
        CurrentStacks = 1;
        Source = nullptr;
        EffectID = TEXT("");
        bIsPaused = false;
    }

    FAuracronActiveStatusEffect(const FAuracronStatusEffect& InEffect, AActor* InSource = nullptr)
        : Effect(InEffect), Source(InSource)
    {
        StartTime = 0.0f;
        RemainingDuration = InEffect.Duration;
        LastTickTime = 0.0f;
        CurrentStacks = 1;
        EffectID = FGuid::NewGuid().ToString();
        bIsPaused = false;
    }
};

/**
 * Componente principal do sistema de combate 3D vertical
 * Coordena e integra os sistemas existentes:
 * - AuracronSigilosBridge: Sistema de sígilos e fusão
 * - AuracronVerticalTransitionsBridge: Transições entre camadas
 * - AuracronRealmsBridge: Sistema de realms dinâmicos
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRONCOMBABRIDGE_API UAuracronCombatBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronCombatBridge();

    // === Lifecycle Methods ===
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    // === Combat Layer Management ===
    
    /** Obtém a camada atual do ator */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    EAuracronCombatLayer GetCurrentCombatLayer() const;

    /** Define a camada de combate do ator */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void SetCombatLayer(EAuracronCombatLayer NewLayer);

    /** Verifica se pode atacar um alvo em outra camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool CanTargetLayer(EAuracronCombatLayer TargetLayer) const;

    /** Calcula modificadores de dano baseado nas camadas */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateLayerDamageModifier(AActor* Target) const;

    /** Verifica se o personagem está em combate */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool IsInCombat() const { return bInCombat; }

    // === Integration with Existing Systems ===
    
    /** Integra com o sistema de sígilos para modificadores de combate */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ApplySigilCombatModifiers();

    /** Integra com transições verticais para mudanças de camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void OnVerticalTransition(EAuracronRealmType FromRealm, EAuracronRealmType ToRealm);

    /** Integra com sistema de realms para bônus específicos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ApplyRealmCombatBonuses();
    
    /** Detecta automaticamente mudanças de altura para transições de camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void CheckForAutomaticLayerTransition();
    
    /** Força uma transição suave entre camadas */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ForceLayerTransition(EAuracronCombatLayer TargetLayer, float TransitionDuration = 1.0f);
    
    /** Verifica se está em zona de transição entre camadas */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool IsInLayerTransitionZone() const;
    
    /** Obtém a camada baseada na posição atual do ator */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    EAuracronCombatLayer GetLayerFromCurrentPosition() const;

    // === Combat Interface Implementation ===
    virtual void OnCombatStarted_Implementation(AActor* Instigator, AActor* Target);
    virtual void OnCombatEnded_Implementation(const TArray<AActor*>& Participants);
    virtual void OnSigilDamageDealt_Implementation(AActor* DamageDealer, AActor* DamageReceiver, float DamageAmount, const FString& SigilType);

    // === Targeting System ===
    
    /** Encontra alvos válidos na camada atual e camadas alcançáveis */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindValidTargetsInLayers(float Range, bool bIncludeOtherLayers = false) const;

    /** Calcula alcance efetivo baseado na camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateEffectiveRange(float BaseRange, EAuracronCombatLayer TargetLayer) const;

    // === Damage Calculation ===
    
    /** Calcula dano final considerando camadas, sígilos e realms */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateFinalDamage(float BaseDamage, AActor* Target, const FString& DamageType = "") const;

    /** Aplica efeitos de área considerando camadas verticais */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ApplyAreaEffect(FVector Location, float Radius, float Damage, bool bAffectAllLayers = false);

    // === Adaptive Damage System ===
    
    /** Calcula dano adaptativo baseado no histórico de combate */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateAdaptiveDamage(float BaseDamage, AActor* Target, const FString& DamageType = "") const;
    
    /** Aplica modificadores de posicionamento tático */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculatePositionalModifier(AActor* Target) const;
    
    /** Sistema de combo baseado em sequência de ataques */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateComboMultiplier(const FString& AttackSequence) const;
    
    /** Detecta combos especiais baseados em sequências específicas */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|Combo")
    FString DetectSpecialCombo() const;
    
    /** Calcula multiplicador para combos especiais */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|Combo")
    float CalculateSpecialComboMultiplier(const FString& ComboName) const;
    
    /** Reseta o sistema de combo */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|Combo")
    void ResetComboSystem();
    
    /** Verifica se um combo está ativo */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|Combo")
    bool IsComboActive() const;
    
    /** Obtém informações detalhadas do combo atual */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|Combo")
    FString GetCurrentComboInfo() const;
    
    /** Atualiza o sistema de combos especiais */
    void UpdateSpecialComboSystem(float DeltaTime);
    
    /** Calcula modificador de elevação (vantagem de altura) */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateElevationAdvantage(AActor* Target) const;
    
    /** Sistema de resistência adaptativa */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateAdaptiveResistance(const FString& DamageType) const;
    
    /** Registra dano recebido para sistema adaptativo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void RegisterDamageReceived(float Damage, const FString& DamageType, AActor* Source);
    
    /** Registra dano causado para sistema adaptativo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void RegisterDamageDealt(float Damage, const FString& DamageType, AActor* Target);

    // === Damage Type System ===
    
    /** Calcula dano adaptativo usando enum de tipos de dano */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateAdaptiveDamageByType(float BaseDamage, AActor* Target, EAuracronDamageType DamageType) const;
    
    /** Calcula resistência adaptativa por tipo de dano */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateAdaptiveResistanceByType(EAuracronDamageType DamageType) const;
    
    /** Registra dano recebido por tipo específico */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void RegisterDamageReceivedByType(float Damage, EAuracronDamageType DamageType, AActor* Source);
    
    /** Aplica modificadores específicos do tipo de dano */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float ApplyDamageTypeModifiers(float BaseDamage, EAuracronDamageType DamageType) const;
    
    /** Converte string de tipo de dano para enum */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    EAuracronDamageType StringToDamageType(const FString& DamageTypeString) const;
    
    /** Converte enum de tipo de dano para string */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    FString DamageTypeToString(EAuracronDamageType DamageType) const;
    
    /** Inicializa modificadores padrão para todos os tipos de dano */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void InitializeDamageTypeModifiers();

    // === Layer Effects System ===
    
    /** Aplica um efeito de camada ao ator */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool ApplyLayerEffect(const FAuracronLayerEffect& Effect, AActor* Source = nullptr);
    
    /** Remove um efeito de camada específico */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool RemoveLayerEffect(const FString& EffectName);
    
    /** Remove todos os efeitos de um tipo específico */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void RemoveLayerEffectsByType(EAuracronLayerEffectType EffectType);

    // ========== STATUS EFFECTS SYSTEM ==========
    
    /** Aplica um status effect */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool ApplyStatusEffect(const FAuracronStatusEffect& StatusEffect, AActor* Source = nullptr);

    /** Aplica um status effect por tipo usando template */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool ApplyStatusEffectByType(EAuracronStatusEffectType StatusType, float Duration = -1.0f, float Magnitude = 1.0f, AActor* Source = nullptr);

    /** Remove um status effect específico por ID */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool RemoveStatusEffect(const FString& EffectID);

    /** Remove status effects por nome */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    int32 RemoveStatusEffectsByName(const FString& EffectName);

    /** Remove status effects por tipo */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    int32 RemoveStatusEffectsByType(EAuracronStatusEffectType StatusType);

    /** Remove status effects por categoria */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    int32 RemoveStatusEffectsByCategory(EAuracronStatusCategory Category);

    /** Remove todos os status effects */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    void ClearAllStatusEffects();

    /** Verifica se tem um status effect ativo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    bool HasStatusEffect(const FString& EffectName) const;

    /** Verifica se tem um status effect de um tipo específico */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    bool HasStatusEffectOfType(EAuracronStatusEffectType StatusType) const;

    /** Obtém um status effect ativo por nome */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    FAuracronActiveStatusEffect GetStatusEffect(const FString& EffectName) const;

    /** Obtém todos os status effects ativos */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    TArray<FAuracronActiveStatusEffect> GetAllActiveStatusEffects() const;

    /** Obtém status effects por tipo */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    TArray<FAuracronActiveStatusEffect> GetStatusEffectsByType(EAuracronStatusEffectType StatusType) const;

    /** Obtém status effects por categoria */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    TArray<FAuracronActiveStatusEffect> GetStatusEffectsByCategory(EAuracronStatusCategory Category) const;

    /** Pausa/despausa um status effect */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool SetStatusEffectPaused(const FString& EffectID, bool bPaused);

    /** Modifica a duração de um status effect */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool ModifyStatusEffectDuration(const FString& EffectID, float NewDuration);

    /** Adiciona stacks a um status effect */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool AddStatusEffectStacks(const FString& EffectName, int32 StacksToAdd);

    /** Remove stacks de um status effect */
    UFUNCTION(BlueprintCallable, Category = "Status Effects")
    bool RemoveStatusEffectStacks(const FString& EffectName, int32 StacksToRemove);

    /** Obtém informações resumidas dos status effects ativos */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Status Effects")
    FString GetStatusEffectsSummary() const;
    
    /** Obtém todos os efeitos ativos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<FAuracronActiveLayerEffect> GetActiveLayerEffects() const;
    
    /** Verifica se tem um efeito específico ativo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool HasLayerEffect(const FString& EffectName) const;
    
    /** Obtém o modificador total de dano dos efeitos ativos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float GetLayerEffectDamageModifier() const;
    
    /** Obtém o modificador total de velocidade dos efeitos ativos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float GetLayerEffectSpeedModifier() const;
    
    /** Obtém o modificador total de defesa dos efeitos ativos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float GetLayerEffectDefenseModifier() const;
    
    /** Aplica efeitos automáticos baseados na camada atual */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ApplyAutomaticLayerEffects();
    
    /** Remove todos os efeitos de camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    void ClearAllLayerEffects();
    
    // === 3D Targeting System ===
    
    /** Encontra alvos em um raio específico com filtros de camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindTargetsInRadius(float Radius, const TArray<EAuracronCombatLayer>& AllowedLayers, bool bRequireLineOfSight = true) const;
    
    /** Encontra alvos em um raio específico (todas as camadas) */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindTargetsInRadiusAllLayers(float Radius, bool bRequireLineOfSight = true) const;
    
    /** Encontra o melhor alvo baseado em critérios específicos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    AActor* FindBestTarget(float MaxRange, const TArray<EAuracronCombatLayer>& PreferredLayers) const;
    
    /** Encontra o melhor alvo (todas as camadas) */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    AActor* FindBestTargetAllLayers(float MaxRange) const;
    
    /** Verifica se um alvo está em alcance válido para a camada atual */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool IsTargetInValidRange(AActor* Target, float& OutDistance) const;
    
    /** Obtém o alcance máximo para a camada atual */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float GetMaxRangeForCurrentLayer() const;
    
    /** Realiza targeting em cone 3D */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindTargetsInCone(float Range, float ConeAngle, const TArray<EAuracronCombatLayer>& AllowedLayers) const;
    
    /** Realiza targeting em cone 3D (todas as camadas) */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindTargetsInConeAllLayers(float Range, float ConeAngle) const;
    
    /** Realiza targeting em esfera 3D com diferentes raios por camada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    TArray<AActor*> FindTargetsInLayeredSphere(const TMap<EAuracronCombatLayer, float>& LayerRanges) const;
    
    /** Verifica linha de visão 3D considerando obstáculos verticais */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    bool HasLineOfSight3D(AActor* Target, bool bIgnoreVerticalObstacles = false) const;
    
    /** Calcula prioridade de alvo baseada em camada e posição */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat")
    float CalculateTargetPriority(AActor* Target) const;
    
    /** Sistema avançado de IA para priorização de alvos */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    float CalculateAdvancedTargetPriority(AActor* Target, const FAuracronTargetingContext& Context) const;
    
    /** Analisa ameaça de um alvo baseado em comportamento */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    float AnalyzeThreatLevel(AActor* Target) const;
    
    /** Calcula valor tático de um alvo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    float CalculateTacticalValue(AActor* Target) const;
    
    /** Prediz movimento futuro do alvo */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    FVector PredictTargetMovement(AActor* Target, float PredictionTime = 1.0f) const;
    
    /** Encontra melhor alvo usando IA avançada */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    AActor* FindBestTargetWithAI(float MaxRange, const FAuracronTargetingContext& Context) const;
    
    /** Avalia eficácia de ataque contra alvo específico */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    float EvaluateAttackEffectiveness(AActor* Target, const FString& AttackType = "") const;
    
    /** Sistema de aprendizado para melhorar targeting */
    UFUNCTION(BlueprintCallable, Category = "Auracron Combat AI")
    void UpdateTargetingLearning(AActor* Target, bool bSuccessfulAttack, float DamageDealt);
    
    /** Calcula score de sobrevivência do alvo */
    UFUNCTION(BlueprintCallable, Category = "Auracron|Combat|AI")
    float CalculateSurvivalScore(AActor* Target) const;
    
    // Função para atualizar rastreamento de movimento dos alvos
    void UpdateMovementTracking();

protected:
    // === Component References ===
    
    /** Referência ao sistema de sígilos */
    UPROPERTY()
    TObjectPtr<UAuracronSigilosBridge> SigilosBridge;

    /** Referência ao sistema de transições verticais */
    UPROPERTY()
    TObjectPtr<UAuracronVerticalTransitionsBridge> VerticalTransitionsBridge;

    /** Referência ao sistema de realms dinâmicos */
    UPROPERTY()
    TObjectPtr<UAuracronRealmsBridge> RealmsBridge;

    /** Referência ao Ability System Component */
    UPROPERTY()
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    // === Combat Layer Data ===
    
    /** Camada de combate atual */
    UPROPERTY(ReplicatedUsing = OnRep_CurrentCombatLayer, BlueprintReadOnly, Category = "Combat Layer")
    EAuracronCombatLayer CurrentCombatLayer = EAuracronCombatLayer::Surface;

    /** Dados de combate por camada */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Combat Layer")
    TMap<EAuracronCombatLayer, FAuracronLayerCombatData> LayerCombatData;

    /** Estado de combate atual */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Combat State")
    bool bInCombat = false;

    /** Tempo de início do combate */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float CombatStartTime = 0.0f;

    // === Adaptive Damage System Data ===
    
    /** Histórico de tipos de dano recebidos */
    UPROPERTY()
    TMap<FString, float> DamageTypeResistance;
    
    /** Histórico de dano recebido por fonte */
    UPROPERTY()
    TMap<TWeakObjectPtr<AActor>, float> DamageReceivedFromSources;
    
    /** Sequência atual de ataques para sistema de combo */
    UPROPERTY()
    TArray<FString> CurrentAttackSequence;
    
    /** Tempo do último ataque para resetar combo */
    UPROPERTY()
    float LastAttackTime = 0.0f;
    
    /** Multiplicador de combo atual */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float CurrentComboMultiplier = 1.0f;
    
    /** Contador de ataques consecutivos */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    int32 ConsecutiveAttacks = 0;
    
    /** Dano total causado nesta sessão de combate */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float TotalDamageDealt = 0.0f;
    
    /** Dano total recebido nesta sessão de combate */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float TotalDamageReceived = 0.0f;
    
    /** Nome do combo especial ativo (se houver) */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    FString ActiveSpecialCombo;
    
    /** Multiplicador do combo especial ativo */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float SpecialComboMultiplier = 1.0f;
    
    /** Tempo restante para o combo especial */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    float SpecialComboTimeRemaining = 0.0f;
    
    /** Número de hits necessários para quebrar o combo */
    UPROPERTY(BlueprintReadOnly, Category = "Combat State")
    int32 ComboBreakThreshold = 5;

    // === Damage Type System Data ===
    
    /** Modificadores de dano por tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Types")
    TMap<EAuracronDamageType, FAuracronDamageTypeModifiers> DamageTypeModifiers;
    
    /** Resistências adaptativas por tipo de dano (usando enum) */
    UPROPERTY(BlueprintReadOnly, Category = "Damage Types")
    TMap<EAuracronDamageType, float> AdaptiveResistances;
    
    /** Histórico de dano recebido por tipo (para análise) */
    UPROPERTY(BlueprintReadOnly, Category = "Damage Types")
    TMap<EAuracronDamageType, float> DamageReceivedByType;

    // === Layer Effects System Data ===
    
    /** Array de efeitos de camada ativos */
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Layer Effects")
    TArray<FAuracronActiveLayerEffect> ActiveLayerEffects;
    
    /** Status effects ativos */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Status Effects")
    TArray<FAuracronActiveStatusEffect> ActiveStatusEffects;

    /** Mapeamento de tipos de status effects para efeitos pré-definidos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effects")
    TMap<EAuracronStatusEffectType, FAuracronStatusEffect> StatusEffectTemplates;

    /** Máximo de status effects simultâneos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effects")
    int32 MaxActiveStatusEffects;

    /** Se deve aplicar efeitos visuais automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status Effects")
    bool bAutoApplyVFX;
    
    /** Efeitos automáticos para Surface */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effects")
    TArray<FAuracronLayerEffect> SurfaceLayerEffects;
    
    /** Efeitos automáticos para Sky */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effects")
    TArray<FAuracronLayerEffect> SkyLayerEffects;
    
    /** Efeitos automáticos para Underground */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Effects")
    TArray<FAuracronLayerEffect> UndergroundLayerEffects;
    
    /** Timer para atualização de efeitos */
    FTimerHandle LayerEffectsUpdateTimer;
    
    // === 3D Targeting System Data ===
    
    /** Alcances máximos por camada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting")
    float SurfaceMaxRange = 1000.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting")
    float SkyMaxRange = 1500.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting")
    float UndergroundMaxRange = 800.0f;
    
    /** Cache de alvos válidos por camada */
    TMap<EAuracronCombatLayer, TArray<TWeakObjectPtr<AActor>>> LayerTargetCache;
    
    /** Tempo da última atualização do cache */
    float LastTargetCacheUpdate = 0.0f;
    
    /** Intervalo de atualização do cache (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting")
    float TargetCacheUpdateInterval = 0.5f;
    
    /** Classes de atores que podem ser alvos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting")
    TArray<TSubclassOf<AActor>> TargetableClasses;
    
    // === Automatic Layer Transition System Data ===
    
    /** Posição anterior para detectar mudanças de altura */
    FVector PreviousPosition;
    
    /** Tempo da última verificação de transição */
    float LastTransitionCheck = 0.0f;
    
    /** Intervalo de verificação de transições automáticas (segundos) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Transitions")
    float TransitionCheckInterval = 0.1f;
    
    /** Altura mínima para camada Sky */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Transitions")
    float SkyLayerMinHeight = 500.0f;
    
    /** Altura máxima para camada Underground */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Transitions")
    float UndergroundLayerMaxHeight = -200.0f;
    
    /** Zona de transição (altura em que pode estar em múltiplas camadas) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Transitions")
    float TransitionZoneHeight = 100.0f;
    
    /** Se está atualmente em transição entre camadas */
    UPROPERTY(BlueprintReadOnly, Category = "Layer Transitions")
    bool bIsInLayerTransition = false;
    
    /** Camada de destino durante transição */
    UPROPERTY(BlueprintReadOnly, Category = "Layer Transitions")
    EAuracronCombatLayer TransitionTargetLayer;
    
    /** Duração da transição atual */
    UPROPERTY(BlueprintReadOnly, Category = "Layer Transitions")
    float CurrentTransitionDuration = 0.0f;
    
    /** Tempo restante da transição */
    UPROPERTY(BlueprintReadOnly, Category = "Layer Transitions")
    float TransitionTimeRemaining = 0.0f;
    
    /** Timer para transições suaves */
    FTimerHandle LayerTransitionTimer;
    
    // === Advanced AI Targeting System Data ===
    
    /** Histórico de comportamento dos alvos para análise de ameaça */
    TMap<TWeakObjectPtr<AActor>, float> ThreatLevels;
    
    /** Dados de aprendizado para melhorar targeting */
    TMap<TWeakObjectPtr<AActor>, float> TargetingSuccessRates;
    
    /** Histórico de movimento dos alvos para predição */
    TMap<TWeakObjectPtr<AActor>, FAuracronMovementHistory> MovementHistory;
    
    /** Configurações de IA para targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    float AIAggressionLevel = 0.5f;
    
    /** Peso para análise de ameaça */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    float ThreatAnalysisWeight = 1.0f;
    
    /** Peso para valor tático */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    float TacticalValueWeight = 1.0f;
    
    /** Tempo máximo para predição de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    float MaxPredictionTime = 2.0f;
    
    /** Tamanho máximo do histórico de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    int32 MaxMovementHistorySize = 10;
    
    /** Intervalo para atualização do histórico de movimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Targeting")
    float MovementTrackingInterval = 0.1f;
    
    /** Último tempo de atualização do histórico */
    float LastMovementUpdate = 0.0f;

    // === Internal Methods ===
    
    /** Inicializa referências aos outros bridges */
    void InitializeBridgeReferences();

    /** Configura dados padrão das camadas */
    void SetupDefaultLayerData();

    /** Atualiza modificadores baseado na camada atual */
    void UpdateLayerModifiers();

    /** Replicação da camada de combate */
    UFUNCTION()
    void OnRep_CurrentCombatLayer();

    /** Valida se o alvo está em alcance considerando camadas */
    bool IsTargetInLayerRange(AActor* Target, float Range) const;

    /** Calcula distância 3D considerando camadas */
    float CalculateLayerDistance(AActor* Target) const;
    
    /** Atualiza resistências adaptativas baseado no histórico */
    void UpdateAdaptiveResistances();
    
    /** Reseta combo se muito tempo passou */
    void CheckComboReset();
    
    /** Calcula bônus de flanqueamento */
    float CalculateFlankingBonus(AActor* Target) const;
    
    /** Verifica se o alvo está em posição vulnerável */
    bool IsTargetInVulnerablePosition(AActor* Target) const;
    
    // === Layer Effects Internal Methods ===
    
    /** Atualiza todos os efeitos de camada ativos */
    void UpdateLayerEffects();

    /** Atualiza sistema de status effects */
    void UpdateStatusEffects(float DeltaTime);

    /** Processa tick de um status effect */
    void ProcessStatusEffectTick(FAuracronActiveStatusEffect& ActiveEffect, float CurrentTime);

    /** Verifica se pode aplicar um status effect */
    bool CanApplyStatusEffect(const FAuracronStatusEffect& StatusEffect) const;

    /** Encontra status effect ativo por nome */
    FAuracronActiveStatusEffect* FindActiveStatusEffect(const FString& EffectName);

    /** Encontra status effect ativo por ID */
    FAuracronActiveStatusEffect* FindActiveStatusEffectByID(const FString& EffectID);

    /** Aplica modificadores de status effects */
    float ApplyStatusEffectModifiers(float BaseValue, const FString& AttributeName) const;

    /** Inicializa templates de status effects */
    void InitializeStatusEffectTemplates();

    /** Aplica efeitos visuais de status effect */
    void ApplyStatusEffectVFX(const FAuracronActiveStatusEffect& ActiveEffect);

    /** Remove efeitos visuais de status effect */
    void RemoveStatusEffectVFX(const FAuracronActiveStatusEffect& ActiveEffect);

    /** Verifica imunidades a status effects */
    bool IsImmuneToStatusEffect(const FAuracronStatusEffect& StatusEffect) const;
    
    /** Remove efeitos expirados */
    void RemoveExpiredLayerEffects();
    
    /** Aplica modificadores de efeitos ao cálculo de dano */
    float ApplyLayerEffectModifiers(float BaseDamage, const FString& DamageType) const;
    
    /** Verifica se um efeito pode ser aplicado */
    bool CanApplyLayerEffect(const FAuracronLayerEffect& Effect) const;
    
    /** Encontra um efeito ativo por nome */
    FAuracronActiveLayerEffect* FindActiveLayerEffect(const FString& EffectName);
    
    /** Inicializa efeitos automáticos padrão */
    void InitializeDefaultLayerEffects();
    
    /** Configura efeitos padrão das camadas */
    void SetupDefaultLayerEffects();
    
    /** Callback para replicação de efeitos ativos */
    UFUNCTION()
    void OnRep_ActiveLayerEffects();
    
    // === 3D Targeting Internal Methods ===
    
    /** Atualiza o cache de alvos por camada */
    void UpdateTargetCache();
    
    // === Performance Optimized Methods ===
    
    /** Versão otimizada da atualização do cache de alvos */
    void OptimizedUpdateTargetCache();
    
    /** Atualização paralela do cache de alvos */
    void UpdateTargetCacheParallel();
    
    /** Atualização sequencial do cache de alvos */
    void UpdateTargetCacheSequential();
    
    /** Versão otimizada da validação de alvos */
    bool IsValidTargetOptimized(AActor* Target) const;
    
    /** Busca otimizada de alvos válidos */
    TArray<AActor*> FindValidTargetsOptimized(float Range, bool bIncludeOtherLayers = false) const;
    
    /** Busca alvos usando cache por camada */
    TArray<AActor*> FindValidTargetsFromLayerCache(float Range, bool bIncludeOtherLayers) const;
    
    /** Busca tradicional de alvos (fallback) */
    TArray<AActor*> FindValidTargetsTraditional(float Range, bool bIncludeOtherLayers) const;
    
    /** Atualiza cache de alvos válidos */
    void UpdateValidTargetsCache(const TArray<AActor*>& ValidTargets) const;
    
    /** Cálculo otimizado de prioridade de alvos */
    float CalculateTargetPriorityOptimized(AActor* Target) const;
    
    /** Log de métricas de performance */
    void LogPerformanceMetrics() const;
    
    /** Verifica se um ator é um alvo válido */
    bool IsValidTarget(AActor* Actor) const;
    
    /** Obtém a camada de combate de um ator */
    EAuracronCombatLayer GetActorCombatLayer(AActor* Actor) const;
    
    /** Calcula distância 3D considerando camadas */
    float CalculateLayerAwareDistance(AActor* Target) const;
    
    /** Filtra alvos por camadas permitidas */
    TArray<AActor*> FilterTargetsByLayers(const TArray<AActor*>& Targets, const TArray<EAuracronCombatLayer>& AllowedLayers) const;
    
    /** Ordena alvos por prioridade */
    void SortTargetsByPriority(TArray<AActor*>& Targets) const;
    
    /** Realizar trace para verificar linha de visão */
    bool PerformLineOfSightTrace(const FVector& Start, const FVector& End, AActor* IgnoreActor = nullptr) const;
    
    /** Inicializar sistema de targeting */
    void InitializeTargetingSystem();

private:
    /** Timer para atualizações de combate */
    FTimerHandle CombatUpdateTimer;

    /** Cache de alvos válidos para otimização */
    mutable TArray<TWeakObjectPtr<AActor>> CachedValidTargets;
    mutable float LastTargetCacheTime = 0.0f;
    static constexpr float TARGET_CACHE_DURATION = 0.1f; // 100ms cache
    
    /** Configurações de performance */
    UPROPERTY()
    const UAuracronCombatPerformanceConfig* PerformanceConfig;

    /** Métricas de performance em tempo real */
    mutable FAuracronPerformanceMetrics CurrentMetrics;
    
    // === Adaptive System Constants ===
    static constexpr float COMBO_RESET_TIME = 3.0f; // Tempo para resetar combo
    static constexpr float MAX_COMBO_MULTIPLIER = 2.5f; // Multiplicador máximo de combo
    static constexpr float RESISTANCE_BUILDUP_RATE = 0.1f; // Taxa de acúmulo de resistência
    static constexpr float MAX_RESISTANCE = 0.5f; // Resistência máxima (50%)
    static constexpr float ELEVATION_BONUS_PER_METER = 0.01f; // Bônus por metro de altura
    static constexpr float MAX_ELEVATION_BONUS = 0.3f; // Bônus máximo de elevação
    
    // === Advanced Combo System Constants ===
    static constexpr float SPECIAL_COMBO_DURATION = 8.0f; // Duração de combos especiais
    static constexpr float SPECIAL_COMBO_BASE_MULTIPLIER = 1.5f; // Multiplicador base para combos especiais
    static constexpr float MAX_SPECIAL_COMBO_MULTIPLIER = 4.0f; // Multiplicador máximo para combos especiais
    static constexpr int32 MIN_COMBO_LENGTH = 3; // Tamanho mínimo para detectar combos especiais
    static constexpr int32 MAX_COMBO_SEQUENCE_LENGTH = 15; // Tamanho máximo da sequência de combo
    static constexpr float FLANKING_ANGLE_THRESHOLD = 120.0f; // Ângulo para flanqueamento
    static constexpr float FLANKING_DAMAGE_BONUS = 0.25f; // Bônus de dano por flanqueamento
};