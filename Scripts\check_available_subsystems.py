#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar quais subsistemas estão disponíveis no UE5.6
"""

import unreal

def check_available_subsystems():
    """Verifica quais subsistemas estão disponíveis no UE5.6"""
    print("=== Verificando Subsistemas Disponíveis no UE5.6 ===")
    
    # Lista de subsistemas para verificar
    subsystems_to_check = [
        'WorldPartitionEditorSubsystem',
        'WorldPartitionSubsystem', 
        'DataLayerEditorSubsystem',
        'DataLayerSubsystem',
        'EditorActorSubsystem',
        'EditorAssetSubsystem',
        'EditorUtilitySubsystem',
        'StaticMeshEditorSubsystem',
        'LevelEditorSubsystem',
        'UnrealEditorSubsystem'
    ]
    
    available_subsystems = []
    unavailable_subsystems = []
    
    for subsystem_name in subsystems_to_check:
        try:
            # Tentar obter a classe do subsistema
            subsystem_class = getattr(unreal, subsystem_name, None)
            if subsystem_class:
                # Tentar obter uma instância do subsistema
                subsystem_instance = unreal.get_editor_subsystem(subsystem_class)
                if subsystem_instance:
                    available_subsystems.append(subsystem_name)
                    print(f"✓ {subsystem_name} - DISPONÍVEL")
                else:
                    unavailable_subsystems.append(subsystem_name)
                    print(f"✗ {subsystem_name} - CLASSE EXISTE MAS INSTÂNCIA FALHOU")
            else:
                unavailable_subsystems.append(subsystem_name)
                print(f"✗ {subsystem_name} - CLASSE NÃO ENCONTRADA")
        except Exception as e:
            unavailable_subsystems.append(subsystem_name)
            print(f"✗ {subsystem_name} - ERRO: {str(e)}")
    
    print("\n=== RESUMO ===")
    print(f"Subsistemas disponíveis: {len(available_subsystems)}")
    print(f"Subsistemas indisponíveis: {len(unavailable_subsystems)}")
    
    if available_subsystems:
        print("\nSubsistemas DISPONÍVEIS:")
        for subsystem in available_subsystems:
            print(f"  - {subsystem}")
    
    if unavailable_subsystems:
        print("\nSubsistemas INDISPONÍVEIS:")
        for subsystem in unavailable_subsystems:
            print(f"  - {subsystem}")
    
    # Verificar métodos alternativos para World Partition
    print("\n=== Verificando Alternativas para World Partition ===")
    
    try:
        # Verificar se existe unreal.WorldPartition
        world_partition_class = getattr(unreal, 'WorldPartition', None)
        if world_partition_class:
            print("✓ unreal.WorldPartition - CLASSE DISPONÍVEL")
        else:
            print("✗ unreal.WorldPartition - CLASSE NÃO ENCONTRADA")
    except Exception as e:
        print(f"✗ unreal.WorldPartition - ERRO: {str(e)}")
    
    try:
        # Verificar se existe unreal.DataLayer
        data_layer_class = getattr(unreal, 'DataLayer', None)
        if data_layer_class:
            print("✓ unreal.DataLayer - CLASSE DISPONÍVEL")
        else:
            print("✗ unreal.DataLayer - CLASSE NÃO ENCONTRADA")
    except Exception as e:
        print(f"✗ unreal.DataLayer - ERRO: {str(e)}")
    
    # Verificar mundo atual
    try:
        editor_world = unreal.get_editor_world()
        if editor_world:
            print(f"✓ Editor World disponível: {editor_world.get_name()}")
            
            # Verificar se o mundo tem World Partition habilitado
            try:
                world_partition = editor_world.get_world_partition()
                if world_partition:
                    print("✓ World Partition habilitado no mundo atual")
                else:
                    print("✗ World Partition NÃO habilitado no mundo atual")
            except Exception as e:
                print(f"✗ Erro ao verificar World Partition: {str(e)}")
        else:
            print("✗ Editor World não disponível")
    except Exception as e:
        print(f"✗ Erro ao obter Editor World: {str(e)}")

if __name__ == "__main__":
    check_available_subsystems()