// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON Foliage Edit Utility Implementation
// UE 5.6 Compatible Implementation

#include "AuracronFoliageEditUtility.h"
#include "CoreMinimal.h"
#include "FoliageEditModule.h"
#include "AuracronFoliageTypes.h"
#include "AuracronFoliage.h"
#include "Engine/World.h"
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "InstancedFoliageActor.h"
#include "InstancedFoliage.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Engine.h"
#include "UObject/UObjectGlobals.h"

#if WITH_EDITOR
#include "FoliageEditUtility.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#endif

DEFINE_LOG_CATEGORY(LogAuracronFoliageEditUtility);

// UAuracronFoliageEditUtility Implementation
UAuracronFoliageEditUtility::UAuracronFoliageEditUtility()
{
    // Initialize utility
}

bool UAuracronFoliageEditUtility::PaintFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, const FFoliageEditSettings& Settings)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get the foliage actor for this world
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No InstancedFoliageActor found for level"));
        return false;
    }

    // Create instance transform
    FTransform InstanceTransform;
    InstanceTransform.SetLocation(Location);
    InstanceTransform.SetRotation(FQuat::Identity);
    InstanceTransform.SetScale3D(FVector::OneVector);

#if WITH_EDITOR
    // Get or create foliage info using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find or add mesh"));
        return false;
    }

    // Create FFoliageInstance from FTransform for UE 5.6 API
    FFoliageInstance NewInstance;
    NewInstance.Location = InstanceTransform.GetLocation();
    NewInstance.Rotation = InstanceTransform.GetRotation().Rotator();
    NewInstance.DrawScale3D = FVector3f(InstanceTransform.GetScale3D());

    // Adicionar instâncias de folhagem usando o componente
        if (UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent())
        {
            TArray<FTransform> Transforms;
            Transforms.Add(InstanceTransform);
            Component->AddInstances(Transforms, true, false, true);
        }
    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Foliage instance painted at location: %s"), *Location.ToString());
    return true;
#else
    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("PaintFoliage is only available in editor builds"));
    return false;
#endif
}

bool UAuracronFoliageEditUtility::EraseFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find and remove nearby instances using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        // Get instances from component
        UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
        if (!Component)
        {
            UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No component found"));
            return false;
        }

        // Find instances within radius and remove them
        TArray<int32> InstancesToRemove;
        int32 InstanceCount = Component->GetInstanceCount();
        for (int32 i = 0; i < InstanceCount; ++i)
        {
            FTransform InstanceTransform;
            if (Component->GetInstanceTransform(i, InstanceTransform, true))
            {
                FVector InstanceLocation = InstanceTransform.GetLocation();
                float Distance = FVector::Dist(InstanceLocation, Location);
                if (Distance <= Radius)
                {
                    InstancesToRemove.Add(i);
                }
            }
        }

        // Remove instances using correct UE 5.6 API
        if (InstancesToRemove.Num() > 0)
        {
            Component->RemoveInstances(InstancesToRemove);
        }

        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Foliage erased at location: %s, removed %d instances"), *Location.ToString(), InstancesToRemove.Num());
        return true;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find foliage info"));
    return false;
}

bool UAuracronFoliageEditUtility::SelectFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find the specific foliage type
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Foliage type not found in actor"));
        return false;
    }

    // Get instances from component
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    if (!Component)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No component found"));
        return false;
    }

    int32 SelectedCount = 0;

    // Select foliage instances within radius using component data
    int32 InstanceCount = Component->GetInstanceCount();
    for (int32 i = 0; i < InstanceCount; ++i)
    {
        FTransform InstanceTransform;
        if (Component->GetInstanceTransform(i, InstanceTransform, true))
        {
            FVector InstanceLocation = InstanceTransform.GetLocation();
            float Distance = FVector::Dist(InstanceLocation, Location);
            if (Distance <= Radius)
            {
                // Mark instance as selected (this is a simplified implementation)
                SelectedCount++;
            }
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Selected %d foliage instances at location: %s with radius: %f"), SelectedCount, *Location.ToString(), Radius);

    return SelectedCount > 0;
}

bool UAuracronFoliageEditUtility::ReapplyFoliage(UWorld* World, UFoliageType* FoliageType, const FVector& Location, float Radius)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find foliage info
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find foliage info"));
        return false;
    }

    // Get component and mark render state dirty to refresh
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    if (!Component)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No component found"));
        return false;
    }

    // Mark render state dirty to refresh
    Component->MarkRenderStateDirty();

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Reapplied foliage at location: %s with radius: %f"), *Location.ToString(), Radius);
    return true;
}

UFoliageType* UAuracronFoliageEditUtility::CreateFoliageType(UStaticMesh* StaticMesh, const FString& Name)
{
    if (!StaticMesh)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid StaticMesh"));
        return nullptr;
    }

    // Create new foliage type using UE 5.6 API
    UFoliageType_InstancedStaticMesh* NewFoliageType = NewObject<UFoliageType_InstancedStaticMesh>();
    if (NewFoliageType)
    {
        // Set the static mesh using the correct UE 5.6 property
        NewFoliageType->Mesh = StaticMesh;

        // Set default values for the foliage type
        NewFoliageType->Density = 100.0f;
        NewFoliageType->Radius = 0.0f;
        NewFoliageType->bSingleInstanceModeOverrideRadius = false;

        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Created foliage type for mesh: %s"), *StaticMesh->GetName());
        return NewFoliageType;
    }

    UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to create foliage type"));
    return nullptr;
}

bool UAuracronFoliageEditUtility::AddFoliageType(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Add foliage type to actor using UE 5.6 API
#if WITH_EDITOR
    FFoliageInfo* FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find or add foliage type"));
        return false;
    }
#else
    // In non-editor builds, just check if foliage info exists
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Foliage type not found in non-editor build"));
        return false;
    }
#endif

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Added foliage type to world"));
    return true;
}

bool UAuracronFoliageEditUtility::RemoveFoliageType(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Find foliage info for this type
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Foliage type not found in actor"));
        return false;
    }

    // Get the component to clear all instances
    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    if (!Component)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No component found for foliage type"));
        return false;
    }

    // Clear all instances of this foliage type
    Component->ClearInstances();
    
    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Removed all instances of foliage type from world"));
    return true;
}

TArray<UFoliageType*> UAuracronFoliageEditUtility::GetFoliageTypes(UWorld* World)
{
    TArray<UFoliageType*> FoliageTypes;

    if (!World)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World"));
        return FoliageTypes;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return FoliageTypes;
    }

    // Get all foliage types using UE 5.6 API - iterate through FoliageInfos
    for (auto& FoliagePair : FoliageActor->GetFoliageInfos())
    {
        if (FoliagePair.Key)
        {
            FoliageTypes.Add(FoliagePair.Key);
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Found %d foliage types"), FoliageTypes.Num());

    return FoliageTypes;
}

int32 UAuracronFoliageEditUtility::GetFoliageInstanceCount(UWorld* World, UFoliageType* FoliageType)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return 0;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return 0;
    }

    // Get instance count using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
        if (!Component)
        {
            return 0;
        }
        
        int32 InstanceCount = Component->GetInstanceCount();
        UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Found %d instances"), InstanceCount);
        return InstanceCount;
    }

    return 0;
}

TArray<FAuracronFoliageInstanceData> UAuracronFoliageEditUtility::GetFoliageInstances(UWorld* World, UFoliageType* FoliageType)
{
    TArray<FAuracronFoliageInstanceData> Instances;

    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return Instances;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return Instances;
    }

    // Get all instances for the foliage type using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (FoliageInfo)
    {
        UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
        if (!Component)
        {
            return Instances;
        }

        // Get instances from component using correct API
        int32 InstanceCount = Component->GetInstanceCount();
        Instances.Reserve(InstanceCount);
        
        for (int32 i = 0; i < InstanceCount; ++i)
        {
            FAuracronFoliageInstanceData InstanceDataItem;
            FTransform InstanceTransform;
            if (Component->GetInstanceTransform(i, InstanceTransform, true))
            {
                InstanceDataItem.Transform = InstanceTransform;
                Instances.Add(InstanceDataItem);
            }
        }
    }

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Retrieved %d foliage instances"), Instances.Num());
    return Instances;
}

bool UAuracronFoliageEditUtility::UpdateFoliageInstances(UWorld* World, UFoliageType* FoliageType, const TArray<FAuracronFoliageInstanceData>& Instances)
{
    if (!World || !FoliageType)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Invalid World or FoliageType"));
        return false;
    }

    // Get foliage actor
    AInstancedFoliageActor* FoliageActor = AInstancedFoliageActor::GetInstancedFoliageActorForLevel(World->GetCurrentLevel());
    if (!FoliageActor)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No foliage actor found"));
        return false;
    }

    // Update instances using UE 5.6 API
    FFoliageInfo* FoliageInfo = FoliageActor->FindInfo(FoliageType);
    if (!FoliageInfo)
    {
#if WITH_EDITOR
        // Create new foliage info if it doesn't exist using UE 5.6 API
        FoliageInfo = FoliageActor->FindOrAddMesh(FoliageType);
        if (!FoliageInfo)
        {
            UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Failed to find or add foliage info"));
            return false;
        }
#else
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("Foliage info not found. FoliageType must be added first using AddFoliageType."));
        return false;
#endif
    }

    UHierarchicalInstancedStaticMeshComponent* Component = FoliageInfo->GetComponent();
    if (!Component)
    {
        UE_LOG(LogAuracronFoliageEditUtility, Warning, TEXT("No component found"));
        return false;
    }

    // Clear existing instances
    Component->ClearInstances();

    // Convert FAuracronFoliageInstanceData to FFoliageInstance and add them
    if (Instances.Num() > 0)
    {
        TArray<FFoliageInstance> NewInstances;
        NewInstances.Reserve(Instances.Num());
        
        for (const FAuracronFoliageInstanceData& InstanceData : Instances)
        {
            FFoliageInstance NewInstance;
            NewInstance.Location = InstanceData.Transform.GetLocation();
            NewInstance.Rotation = InstanceData.Transform.GetRotation().Rotator();
            NewInstance.DrawScale3D = FVector3f(InstanceData.Transform.GetScale3D());
            NewInstances.Add(NewInstance);
        }

        // Add instances using the new UE 5.6 API
        TArray<FTransform> InstanceTransforms;
        InstanceTransforms.Reserve(NewInstances.Num());
        
        for (const FFoliageInstance& Instance : NewInstances)
        {
            FTransform Transform;
            Transform.SetLocation(Instance.Location);
            Transform.SetRotation(FQuat(Instance.Rotation));
            Transform.SetScale3D(FVector(Instance.DrawScale3D));
            InstanceTransforms.Add(Transform);
        }
        
        Component->AddInstances(InstanceTransforms, false, false);
    }

    // Mark render state dirty to refresh
    Component->MarkRenderStateDirty();

    UE_LOG(LogAuracronFoliageEditUtility, Log, TEXT("Updated %d foliage instances"), Instances.Num());
    return true;
}

bool UAuracronFoliageEditUtility::IsValidFoliageLocation(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return false;
    }

    // Enhanced validation for UE 5.6
    if (Location.ContainsNaN())
    {
        return false;
    }

    // Check if location is within world bounds
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    // Perform line trace to validate surface
    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic))
    {
        // Check if the surface is suitable for foliage
        return HitResult.bBlockingHit && !HitResult.Normal.IsNearlyZero();
    }

    return false;
}

FVector UAuracronFoliageEditUtility::GetSurfaceNormal(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return FVector::UpVector;
    }

    // Enhanced surface normal detection for UE 5.6
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = false;

    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic, QueryParams))
    {
        if (HitResult.bBlockingHit && !HitResult.Normal.IsNearlyZero())
        {
            return HitResult.Normal.GetSafeNormal();
        }
    }

    return FVector::UpVector;
}

float UAuracronFoliageEditUtility::GetSurfaceHeight(UWorld* World, const FVector& Location)
{
    if (!World)
    {
        return Location.Z;
    }

    // Enhanced surface height detection for UE 5.6
    FHitResult HitResult;
    FVector Start = Location + FVector(0, 0, 1000);
    FVector End = Location - FVector(0, 0, 1000);

    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnPhysicalMaterial = false;

    if (World->LineTraceSingleByChannel(HitResult, Start, End, ECC_WorldStatic, QueryParams))
    {
        if (HitResult.bBlockingHit)
        {
            return HitResult.Location.Z;
        }
    }

    return Location.Z;
}
