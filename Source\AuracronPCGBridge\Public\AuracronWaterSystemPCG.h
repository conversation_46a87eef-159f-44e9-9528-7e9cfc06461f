// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Water System Generator Header
// Bridge 2.2: PCG Framework - Procedural Water Networks

#pragma once

#include "CoreMinimal.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "AuracronPCGBase.h"
#include "Components/SplineComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "NiagaraSystem.h"
#include "Sound/SoundBase.h"
#include "AuracronWaterSystemPCG.generated.h"

// Forward declarations
class UWaterBodyRiverComponent;
class UWaterSplineComponent;
class ULandscapeWaterInfo;
class UNiagaraComponent;
class UAudioComponent;

/**
 * Configuration for individual water feature (river, stream, creek)
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronWaterFeatureConfig
{
    GENERATED_BODY()

    /** Water feature name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    FString FeatureName;

    /** Start location of the water feature */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    FVector StartLocation;

    /** End location of the water feature */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    FVector EndLocation;

    /** Intermediate waypoints for natural flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    TArray<FVector> Waypoints;

    /** Water width at start */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature", meta = (ClampMin = "50.0", ClampMax = "2000.0"))
    float StartWidth = 200.0f;

    /** Water width at end */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature", meta = (ClampMin = "50.0", ClampMax = "2000.0"))
    float EndWidth = 300.0f;

    /** Water depth */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature", meta = (ClampMin = "10.0", ClampMax = "500.0"))
    float WaterDepth = 100.0f;

    /** Flow velocity (units per second) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature", meta = (ClampMin = "10.0", ClampMax = "1000.0"))
    float FlowVelocity = 150.0f;

    /** Water material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    TSoftObjectPtr<UMaterialInterface> WaterMaterial;

    /** Enable water physics simulation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    bool bEnablePhysicsSimulation = true;

    /** Enable water sound effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    bool bEnableAudioEffects = true;

    /** Enable water particle effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    bool bEnableParticleEffects = true;

    /** Water color tint */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Feature")
    FLinearColor WaterColor = FLinearColor(0.2f, 0.6f, 0.8f, 0.7f);

    /** Connects to crystal plateau (index) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connections")
    int32 ConnectedPlateauIndex = -1;

    /** Connects to living canyon (index) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Connections")
    int32 ConnectedCanyonIndex = -1;

    FAuracronWaterFeatureConfig()
    {
        FeatureName = TEXT("Unnamed Water Feature");
        StartLocation = FVector::ZeroVector;
        EndLocation = FVector::ZeroVector;
    }
};

/**
 * Water system performance configuration
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronWaterPerformanceConfig
{
    GENERATED_BODY()

    /** Maximum number of water spline points */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "10", ClampMax = "500"))
    int32 MaxSplinePoints = 100;

    /** Water LOD distance for physics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "20000.0"))
    float PhysicsLODDistance = 5000.0f;

    /** Water LOD distance for audio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "500.0", ClampMax = "10000.0"))
    float AudioLODDistance = 3000.0f;

    /** Water LOD distance for particles */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "15000.0"))
    float ParticleLODDistance = 4000.0f;

    /** Enable performance optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceOptimization = true;

    /** Use simplified water at distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseSimplifiedWaterAtDistance = true;
};

/**
 * Auracron Water System PCG Settings
 * Generates procedural water networks connecting geological features
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONPCGBRIDGE_API UAuracronWaterSystemPCGSettings : public UAuracronTerrainPCGSettings
{
    GENERATED_BODY()

public:
    UAuracronWaterSystemPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    //~Begin UPCGSettings interface
    virtual FPCGElementPtr CreateElement() const override;
    //~End UPCGSettings interface

    /** Array of water feature configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water System")
    TArray<FAuracronWaterFeatureConfig> WaterFeatures;

    // === Water Assets ===
    
    /** Water body river mesh */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<UStaticMesh> RiverMesh;

    /** Water spline mesh */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<UStaticMesh> SplineMesh;

    /** Water material for rivers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<UMaterialInterface> RiverMaterial;

    /** Water material for streams */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<UMaterialInterface> StreamMaterial;

    /** Water particle system */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<UNiagaraSystem> WaterParticleSystem;

    /** Water flow sound */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Water Assets")
    TSoftObjectPtr<USoundBase> WaterFlowSound;

    // === Performance Settings ===
    
    /** Performance configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    FAuracronWaterPerformanceConfig PerformanceConfig;

    // === Integration Settings ===
    
    /** Use VFX Bridge for water effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseVFXBridge = true;

    /** Use Audio Bridge for water sounds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseAudioBridge = true;

    /** Use Physics Bridge for water simulation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUsePhysicsBridge = true;
};

/**
 * Auracron Water System PCG Element
 * Implements the procedural generation logic for water networks
 */
class AURACRONPCGBRIDGE_API FAuracronWaterSystemPCGElement : public IPCGElement
{
public:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;

protected:
    /** Generate water splines connecting geological features */
    void GenerateWaterSplines(FPCGContext* Context, const UAuracronWaterSystemPCGSettings* Settings, FPCGDataCollection& OutputData) const;

    /** Create water body components */
    void CreateWaterBodies(FPCGContext* Context, const UAuracronWaterSystemPCGSettings* Settings, const FAuracronWaterFeatureConfig& Config) const;

    /** Setup water physics simulation */
    void SetupWaterPhysics(FPCGContext* Context, const UAuracronWaterSystemPCGSettings* Settings, const FAuracronWaterFeatureConfig& Config) const;

    /** Apply water visual effects */
    void ApplyWaterEffects(FPCGContext* Context, const UAuracronWaterSystemPCGSettings* Settings, const FAuracronWaterFeatureConfig& Config) const;

    /** Validate water feature placement */
    bool ValidateWaterPlacement(const FAuracronWaterFeatureConfig& Config, const UAuracronWaterSystemPCGSettings* Settings) const;

    /** Calculate optimal water path between two points */
    TArray<FVector> CalculateWaterPath(const FVector& StartLocation, const FVector& EndLocation, const TArray<FVector>& Waypoints) const;

    /** Generate water spline points with natural curvature */
    TArray<FVector> GenerateNaturalWaterSpline(const TArray<FVector>& PathPoints, int32 MaxSplinePoints) const;

    /** Create optimized water instances for performance */
    void GenerateOptimizedWaterInstances(FPCGContext* Context, const UAuracronWaterSystemPCGSettings* Settings, const TArray<FVector>& SplinePoints, FPCGDataCollection& OutputData) const;

    /** Setup water LOD system */
    void SetupWaterLODSystem(const UAuracronWaterSystemPCGSettings* Settings, const FAuracronWaterFeatureConfig& Config) const;

    /** Calculate water flow direction and velocity */
    FVector CalculateWaterFlow(const FVector& CurrentPoint, const FVector& NextPoint, float FlowVelocity) const;

    /** Apply water erosion effects to terrain */
    void ApplyWaterErosion(FPCGContext* Context, const TArray<FVector>& WaterPath, float ErosionStrength) const;
};